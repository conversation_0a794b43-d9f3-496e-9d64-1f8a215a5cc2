@extends('layouts.layoutMaster')

@section('title', __('Service Category Item Details'))

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-10 col-xl-8">
        <div class="card shadow-sm mb-4">
            <div class="card-header d-flex justify-content-between align-items-center bg-light border-bottom">
                <h4 class="mb-0 fw-bold text-primary">
                    <i class="bx bx-info-circle me-2"></i>{{ __('Service Category Item Details') }}
                </h4>
                <a href="{{ route('service_category_items.index') }}" class="btn btn-outline-secondary">
                    <i class="bx bx-arrow-back"></i> {{ __('Back') }}
                </a>
            </div>
            <div class="card-body">
                <dl class="row gx-3 gy-2">
                    <dt class="col-sm-4 text-muted">ID</dt>
                    <dd class="col-sm-8 fw-semibold">{{ $item->id }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Title') }}</dt>
                    <dd class="col-sm-8">{{ $item->title }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Content') }}</dt>
                    <dd class="col-sm-8">{{ $item->content }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Category') }}</dt>
                    <dd class="col-sm-8">{{ $item->serviceCategory->name ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('User') }}</dt>
                    <dd class="col-sm-8">{{ $item->user->name ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Active') }}</dt>
                    <dd class="col-sm-8">
                        @if($item->active)
                            <span class="badge bg-success">{{ __('Approved') }}</span>
                        @else
                            <span class="badge bg-warning text-dark">{{ __('Pending') }}</span>
                        @endif
                    </dd>
                    <dt class="col-sm-4 text-muted">{{ __('Created At') }}</dt>
                    <dd class="col-sm-8">{{ $item->created_at }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Updated At') }}</dt>
                    <dd class="col-sm-8">{{ $item->updated_at }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Property Type') }}</dt>
                    <dd class="col-sm-8">{{ $item->property_type_id ? optional($item->propertyType)->name : '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('City') }}</dt>
                    <dd class="col-sm-8">{{ optional($item->city)->name ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Image') }}</dt>
                    <dd class="col-sm-8">
                        @if($item->image)
                            <img src="{{ $item->image }}" alt="Image" class="img-thumbnail" style="max-width:150px;">
                        @else - @endif
                    </dd>
                    <dt class="col-sm-4 text-muted">{{ __('Video') }}</dt>
                    <dd class="col-sm-8">
                        @if($item->video)
                            <a href="{{ $item->video }}" target="_blank" class="btn btn-outline-primary btn-sm"><i class="bx bx-play"></i> {{ __('View Video') }}</a>
                        @else - @endif
                    </dd>
                    <dt class="col-sm-4 text-muted">{{ __('Price') }}</dt>
                    <dd class="col-sm-8">{{ $item->price ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Weekend Price') }}</dt>
                    <dd class="col-sm-8">{{ $item->weekend_price ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Week Price') }}</dt>
                    <dd class="col-sm-8">{{ $item->week_price ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Month Price') }}</dt>
                    <dd class="col-sm-8">{{ $item->month_price ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Latitude') }}</dt>
                    <dd class="col-sm-8">{{ $item->lat ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Longitude') }}</dt>
                    <dd class="col-sm-8">{{ $item->lon ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Address') }}</dt>
                    <dd class="col-sm-8">{{ $item->address ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Confirmation') }}</dt>
                    <dd class="col-sm-8">{{ $item->confirmation ? __('Yes') : __('No') }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Form') }}</dt>
                    <dd class="col-sm-8">{{ $item->service_category_form_id ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Views') }}</dt>
                    <dd class="col-sm-8">{{ $item->views ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Rating') }}</dt>
                    <dd class="col-sm-8">{{ $item->rating ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('No. of Rates') }}</dt>
                    <dd class="col-sm-8">{{ $item->no_of_rates ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('No. of Guests') }}</dt>
                    <dd class="col-sm-8">{{ $item->no_guests ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Beds') }}</dt>
                    <dd class="col-sm-8">{{ $item->beds ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Baths') }}</dt>
                    <dd class="col-sm-8">{{ $item->baths ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Booking Rules') }}</dt>
                    <dd class="col-sm-8">{{ $item->booking_rules ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Cancelation Rules') }}</dt>
                    <dd class="col-sm-8">{{ $item->cancelation_rules ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Cancelation Policy') }}</dt>
                    <dd class="col-sm-8">{{ optional($item->cancellationPolicy)->name ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Short Term Policy') }}</dt>
                    <dd class="col-sm-8">{{ optional($item->shortTermPolicy)->name ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Long Term Policy') }}</dt>
                    <dd class="col-sm-8">{{ optional($item->longTermPolicy)->name ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Include Commission Daily') }}</dt>
                    <dd class="col-sm-8">{{ $item->include_commission_daily ? __('Yes') : __('No') }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Include Commission Weekly') }}</dt>
                    <dd class="col-sm-8">{{ $item->include_commission_weekly ? __('Yes') : __('No') }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Include Commission Monthly') }}</dt>
                    <dd class="col-sm-8">{{ $item->include_commission_monthly ? __('Yes') : __('No') }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Tourism Permit Number') }}</dt>
                    <dd class="col-sm-8">{{ $item->tourism_permit_number ?? '-' }}</dd>
                    <dt class="col-sm-4 text-muted">{{ __('Tourism Permit Document') }}</dt>
                    <dd class="col-sm-8">
                        @if($item->tourism_permit_document)
                            <a href="{{ asset('storage/'.$item->tourism_permit_document) }}" target="_blank" class="btn btn-outline-info btn-sm">
                                <i class="bx bx-file"></i> {{ __('View Document') }}
                            </a>
                        @else - @endif
                    </dd>
                    <dt class="col-sm-4 text-muted">{{ __('Gallery') }}</dt>
                    <dd class="col-sm-8">
                        @if($item->gallery && $item->gallery->count())
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($item->gallery as $gallery)
                                    <a href="{{ asset($gallery->image) }}" target="_blank">
                                        <img src="{{ asset($gallery->image) }}" alt="Gallery Image" class="img-thumbnail border border-2" style="max-width:80px; max-height:80px; border-radius:6px;">
                                    </a>
                                @endforeach
                            </div>
                        @else - @endif
                    </dd>
                    <dt class="col-sm-4 text-muted">{{ __('Facilities') }}</dt>
                    <dd class="col-sm-8">
                        @if($item->facilities && $item->facilities->count())
                            <div class="d-flex flex-wrap gap-1">
                                @foreach($item->facilities as $facility)
                                    <span class="badge bg-info text-dark mb-1 px-2 py-1">{{ $facility->name ?? $facility->title_en ?? $facility->title ?? '-' }}</span>
                                @endforeach
                            </div>
                        @else - @endif
                    </dd>
                </dl>
                @if(!$item->active)
                <div class="mt-4 text-end">
                    <form method="POST" action="{{ route('service_category_items.approve', $item->id) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success px-4"><i class="bx bx-check"></i> {{ __('Approve') }}</button>
                    </form>
                    <button type="button" class="btn btn-danger px-4" data-bs-toggle="modal" data-bs-target="#declineModal"><i class="bx bx-x"></i> {{ __('Decline') }}</button>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Decline Modal -->
<div class="modal fade" id="declineModal" tabindex="-1" aria-labelledby="declineModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="POST" action="{{ route('service_category_items.decline', $item->id) }}">
        @csrf
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="declineModalLabel"><i class="bx bx-x"></i> {{ __('Decline Item') }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="decline_reason" class="form-label fw-semibold">{{ __('Reason for Decline') }}</label>
              <textarea class="form-control" id="decline_reason" name="decline_reason" rows="3" required></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
            <button type="submit" class="btn btn-danger">{{ __('Decline') }}</button>
          </div>
        </div>
    </form>
  </div>
</div>
@endsection
