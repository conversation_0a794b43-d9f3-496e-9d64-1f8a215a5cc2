import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/components/build_page_with_default_transition.dart';
import 'package:gather_point/core/navigation_bar/main_navigation_bar.dart';
import 'package:gather_point/core/routing/routes_branches.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/views/no_internet_view.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/presentation/views/login_view.dart';
import 'package:gather_point/feature/auth/presentation/views/verify_otp.dart';
import 'package:gather_point/feature/onBoarding/presentation/manager/Onboarding_cubit/onboarding_cubit.dart';
import 'package:gather_point/feature/onBoarding/presentation/views/on_boarding_view.dart';
import 'package:gather_point/feature/splash/presentation/views/splash_view.dart';
import 'package:gather_point/feature/profile/presentation/views/account_settings_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/help_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/privacy_settings_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/view_profile_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/edit_profile_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/refer_host_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/legal_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/notification_settings_screen.dart';
import 'package:gather_point/feature/host/presentation/views/create_property_wizard.dart';
import 'package:gather_point/feature/host/routes/host_routes.dart';
import 'package:go_router/go_router.dart';

import 'app_router.dart';

List<RouteBase> appRoutes = [
  StatefulShellRoute.indexedStack(
    builder: (context, state, navigationShell) {
      return MainNavigationBar(navigationShell: navigationShell);
    },
    branches: routesBranches(),
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kSplashView,
    builder: (context, state) {
      return const SplashView();
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kNoInternetView,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const NoInternetView(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kOnBoardingView,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: BlocProvider(
          create: (context) => OnboardingCubit(),
          child: const OnBoardingView(),
        ),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kVerifyOTPView,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const VerifyOTPView(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kLoginView,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const LoginView(),
      );
    },
  ),

  // Profile Routes
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kAccountSettings,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const AccountSettingsScreen(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kHelp,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const HelpScreen(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kPrivacySettings,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const PrivacySettingsScreen(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kViewProfile,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const ViewProfileScreen(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kEditProfile,
    pageBuilder: (context, state) {
      // Get user data from extra parameters
      final args = state.extra as Map<String, dynamic>?;
      final user = args?['user'] as UserEntity?;

      if (user == null) {
        // If no user provided, redirect to profile view
        return buildPageWithDefaultTransition<void>(
          context: context,
          state: state,
          child: const Scaffold(
            body: Center(
              child: Text('خطأ: لا يمكن تحميل بيانات المستخدم'),
            ),
          ),
        );
      }

      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: EditProfileScreen(user: user),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kReferHost,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const ReferHostScreen(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kLegal,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const LegalScreen(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kNotificationSettings,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const NotificationSettingsScreen(),
      );
    },
  ),

  // Host Routes
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kCreateProperty,
    pageBuilder: (context, state) {
      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: const CreatePropertyWizard(),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kEditProperty,
    pageBuilder: (context, state) {
      final args = state.extra as Map<String, dynamic>?;
      final propertyId = args?['propertyId'] as int?;
      final propertyData = args?['propertyData'];

      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: HostRoutes.buildEditPropertyPage(context, propertyId, propertyData),
      );
    },
  ),
  GoRoute(
    parentNavigatorKey: parentKey,
    path: RoutesKeys.kPropertyAnalytics,
    pageBuilder: (context, state) {
      final args = state.extra as Map<String, dynamic>?;
      final propertyId = args?['propertyId'] as int?;
      final propertyTitle = args?['propertyTitle'] as String?;

      return buildPageWithDefaultTransition<void>(
        context: context,
        state: state,
        child: HostRoutes.buildAnalyticsPage(context, propertyId, propertyTitle),
      );
    },
  ),
];
