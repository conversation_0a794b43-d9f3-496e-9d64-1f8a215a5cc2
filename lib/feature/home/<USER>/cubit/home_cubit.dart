import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'dart:developer' as developer;

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  final DioConsumer _dioConsumer;
  final LocationService _locationService;

  HomeCubit({
    required DioConsumer dioConsumer,
    required LocationService locationService,
  })  : _dioConsumer = dioConsumer,
        _locationService = locationService,
        super(HomeInitial());

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(HomeState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Initialize home data
  Future<void> initializeHome() async {
    _safeEmit(HomeLoading());

    try {
      // First, load cities (this must happen first)
      await loadCitiesAndSelectClosest();

      // Get current state to access selected city
      final currentState = state;
      if (currentState is HomeLoaded) {
        // Show shimmer while loading data
        _safeEmit(currentState.copyWith(
          isLoadingCategories: true,
          isLoadingReels: true,
        ));

        // Load data with selected city (or null if no city)
        await Future.wait([
          fetchCategories(currentState.currentCity?.id, setLoading: false),
          fetchReels(currentState.currentCity?.id, setLoading: false),
        ]);
      }
    } catch (e) {
      _safeEmit(HomeError('Failed to initialize home: ${e.toString()}'));
    }
  }

  /// Load cities and select closest based on location
  Future<void> loadCitiesAndSelectClosest() async {
    try {
      final hasPermission = await _locationService.checkLocationPermissions();

      if (hasPermission) {
        // User granted location permission - get current location
        try {
          final position = await Geolocator.getCurrentPosition();
          final cities = await _locationService.getCitiesAndClosest(position);

          final currentState = state;
          if (currentState is HomeLoaded) {
            _safeEmit(currentState.copyWith(
              currentCity: cities['closestCity'],
              cities: cities['allCities'],
              isLoadingCategories: true,
              isLoadingReels: true,
            ));
          } else {
            _safeEmit(HomeLoaded(
              currentCity: cities['closestCity'],
              cities: cities['allCities'],
              isLoadingCategories: true,
              isLoadingReels: true,
            ));
          }
          return;
        } catch (e) {
          developer.log('Failed to get current location: $e', name: 'HomeCubit');
          // Fall through to load cities without location
        }
      }

      // No permission or location failed - load cities with default location (Riyadh)
      await loadCitiesWithDefaultLocation();

    } catch (e) {
      developer.log('Failed to load cities: $e', name: 'HomeCubit');
      // Continue with empty cities list if everything fails
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(cities: []));
      } else {
        _safeEmit(const HomeLoaded(cities: []));
      }
    }
  }

  /// Load cities using default location (Riyadh coordinates)
  Future<void> loadCitiesWithDefaultLocation() async {
    try {
      // Use Riyadh coordinates as default
      const defaultLat = 24.7136;
      const defaultLng = 46.6753;

      final response = await _dioConsumer.post(
        '/api/general/cities',
        data: {'lat': defaultLat, 'lng': defaultLng},
      );

      if (response['data'] != null) {
        final data = response['data'];
        final citiesList = (data['cities'] as List)
            .map((json) => City.fromJson(json))
            .toList();

        // Select first city as default (usually closest to Riyadh)
        final defaultCity = citiesList.isNotEmpty ? citiesList.first : null;

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            currentCity: defaultCity,
            cities: citiesList,
            isLoadingCategories: true,
            isLoadingReels: true,
          ));
        } else {
          _safeEmit(HomeLoaded(
            currentCity: defaultCity,
            cities: citiesList,
            isLoadingCategories: true,
            isLoadingReels: true,
          ));
        }
      }
    } catch (e) {
      developer.log('Failed to load cities with default location: $e', name: 'HomeCubit');
      rethrow;
    }
  }

  /// Select a specific city and refetch data
  Future<void> selectCity(City city) async {
    developer.log('Selecting city: ${city.name} (ID: ${city.id})', name: 'HomeCubit');

    final currentState = state;
    if (currentState is HomeLoaded) {
      // Update current city and set loading states
      _safeEmit(currentState.copyWith(
        currentCity: city,
        isLoadingCategories: true,
        isLoadingReels: true,
        categories: [], // Clear old data
        reels: [], // Clear old data
      ));

      developer.log('Starting to fetch data for city: ${city.id}', name: 'HomeCubit');

      // Refresh data with new city
      try {
        await Future.wait([
          fetchCategories(city.id, setLoading: false),
          fetchReels(city.id, setLoading: false),
        ]);
        developer.log('Successfully fetched data for city: ${city.id}', name: 'HomeCubit');
      } catch (e) {
        developer.log('Error fetching data for city ${city.id}: $e', name: 'HomeCubit');
      }
    }
  }

  /// Fetch categories with optional city filter
  Future<void> fetchCategories(int? cityId, {bool setLoading = true}) async {
    final currentState = state;
    if (currentState is HomeLoaded && setLoading) {
      _safeEmit(currentState.copyWith(isLoadingCategories: true));
    }

    try {
      developer.log('Fetching categories with cityId: $cityId', name: 'HomeCubit');
      final queryParams = <String, dynamic>{
        'service_category_id': 1,
      };

      if (cityId != null) {
        queryParams['city_id'] = cityId;
      }

      final response = await _dioConsumer.get(
        '/api/service_categories/list',
        queryParameters: queryParams,
      );

      if (response['data'] != null) {
        final List data = response['data'];
        final categories = data.map((json) => ServiceCategory.fromJson(json)).toList();

        developer.log('Fetched ${categories.length} categories for cityId: $cityId', name: 'HomeCubit');

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            categories: categories,
            isLoadingCategories: false,
          ));
          developer.log('Updated state with ${categories.length} categories', name: 'HomeCubit');
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(isLoadingCategories: false));
      }
      // Don't emit error for individual operations, just log
      developer.log('Failed to load categories: $e', name: 'HomeCubit');
    }
  }

  /// Fetch reels with optional city filter and pagination
  Future<void> fetchReels(int? cityId, {bool setLoading = true, int page = 1, bool loadMore = false}) async {
    final currentState = state;
    if (currentState is HomeLoaded && setLoading) {
      _safeEmit(currentState.copyWith(isLoadingReels: true));
    }

    try {
      developer.log('Fetching reels with cityId: $cityId, page: $page', name: 'HomeCubit');
      final queryParams = <String, dynamic>{
        'service_category_id': 2,
        'reels': 1,
        'page': page,
        'limit': 10,
      };

      if (cityId != null) {
        queryParams['city_id'] = cityId;
      }

      final response = await _dioConsumer.get(
        '/api/items/list',
        queryParameters: queryParams,
      );

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final List<Map<String, dynamic>> newReels =
            List<Map<String, dynamic>>.from(data['items'] ?? []);
        final pagination = data['pagination'];

        developer.log('Fetched ${newReels.length} reels for cityId: $cityId, page: $page', name: 'HomeCubit');

        final currentState = state;
        if (currentState is HomeLoaded) {
          List<Map<String, dynamic>> updatedReels;
          if (loadMore && page > 1) {
            // Append new reels to existing ones
            updatedReels = [...currentState.reels, ...newReels];
          } else {
            // Replace reels (first page or refresh)
            updatedReels = newReels;
          }

          _safeEmit(currentState.copyWith(
            reels: updatedReels,
            isLoadingReels: false,
            reelsPagination: pagination,
          ));
          developer.log('Updated state with ${updatedReels.length} total reels', name: 'HomeCubit');
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(isLoadingReels: false));
      }
      // Don't emit error for individual operations, just log
      developer.log('Failed to load reels: $e', name: 'HomeCubit');
    }
  }

  /// Load more reels for pagination
  Future<void> loadMoreReels() async {
    final currentState = state;
    if (currentState is HomeLoaded &&
        currentState.reelsPagination != null &&
        currentState.reelsPagination!['has_more'] == true &&
        !currentState.isLoadingReels) {

      final nextPage = (currentState.reelsPagination!['current_page'] as int) + 1;
      await fetchReels(
        currentState.currentCity?.id,
        setLoading: false,
        page: nextPage,
        loadMore: true
      );
    }
  }

  /// Perform search
  Future<void> performSearch(String query) async {
    if (query.trim().isEmpty) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(searchResults: []));
      }
      return;
    }

    final currentState = state;
    if (currentState is HomeLoaded) {
      _safeEmit(currentState.copyWith(isSearching: true));
    }

    try {
      final response = await _dioConsumer.get(
        '/api/items/search',
        queryParameters: {
          'keyword': query.trim(),
          'page': 1,
          'limit': 20,
        },
      );

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final List<Map<String, dynamic>> results =
            List<Map<String, dynamic>>.from(data['items'] ?? []);

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            searchResults: results,
            isSearching: false,
          ));
        }
      } else {
        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            searchResults: [],
            isSearching: false,
          ));
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(
          searchResults: [],
          isSearching: false,
        ));
      }
    }
  }

  /// Clear search results
  void clearSearch() {
    final currentState = state;
    if (currentState is HomeLoaded) {
      _safeEmit(currentState.copyWith(searchResults: []));
    }
  }

  /// Update localization and refresh data
  void updateLocalization() {
    _dioConsumer.updateLocalization();
    
    final currentState = state;
    if (currentState is HomeLoaded) {
      // Refresh data with current city
      Future.wait([
        fetchCategories(currentState.currentCity?.id, setLoading: true),
        fetchReels(currentState.currentCity?.id, setLoading: true),
      ]);
    }
  }
}
