import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/shimmer_components.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/home/<USER>/widgets/reel_card_widget.dart';
import 'package:gather_point/feature/home/<USER>/cubit/home_cubit.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:go_router/go_router.dart';

class HomeReelsSection extends StatefulWidget {
  final List<Map<String, dynamic>> reels;
  final bool isLoading;
  final Map<String, dynamic>? pagination;

  const HomeReelsSection({
    super.key,
    required this.reels,
    required this.isLoading,
    this.pagination,
  });

  @override
  State<HomeReelsSection> createState() => _HomeReelsSectionState();
}

class _HomeReelsSectionState extends State<HomeReelsSection> {
  final ScrollController _scrollController = ScrollController();
  bool _isScrolling = false;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Handle scroll sound
    if (!_isScrolling && _scrollController.position.isScrollingNotifier.value) {
      _isScrolling = true;
      _playScrollSound();
    } else if (_isScrolling && !_scrollController.position.isScrollingNotifier.value) {
      _isScrolling = false;
    }

    // Handle pagination
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      _loadMoreReels();
    }
  }

  void _loadMoreReels() {
    if (_isLoadingMore || widget.isLoading) return;

    final hasMore = widget.pagination?['has_more'] == true;
    if (!hasMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      context.read<HomeCubit>().loadMoreReels().then((_) {
        if (mounted) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      });
    } catch (e) {
      debugPrint('Error loading more reels: $e');
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  void _playScrollSound() async {
    try {
      await SoundManager.playScrollSound();
    } catch (e) {
      debugPrint('Scroll sound error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Column(
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                s.reels,
                style: AppTextStyles.font20Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () async {
                  // Play click sound
                  try {
                    await SoundManager.playClickSound();
                  } catch (e) {
                    debugPrint('Click sound error: $e');
                  }

                  if (context.mounted) {
                    // Navigate to reels tab in bottom navigation
                    context.go(RoutesKeys.kFavoritesViewTab);
                  }
                },
                child: Text(
                  s.seeAll,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.accentColor,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 5),

        // Reels List
        SizedBox(
          height: 200,
          child: widget.isLoading
              ? ShimmerComponents.reelsList(context)
              : widget.reels.isEmpty
                  ? _buildEmptyState(context)
                  : ListView.builder(
                      controller: _scrollController,
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      itemCount: widget.reels.length + (_isLoadingMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        // Show loading indicator at the end
                        if (index == widget.reels.length) {
                          return Container(
                            width: 60,
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }

                        final item = widget.reels[index];
                        return TweenAnimationBuilder<double>(
                          duration: Duration(milliseconds: 600 + (index * 150)),
                          tween: Tween(begin: 0.0, end: 1.0),
                          builder: (context, value, child) {
                            return Transform.translate(
                              offset: Offset(50 * (1 - value), 0),
                              child: Opacity(
                                opacity: value,
                                child: Transform.scale(
                                  scale: 0.8 + (0.2 * value),
                                  child: ReelCardWidget(
                                    item: item,
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_circle_outline,
            size: 48,
            color: context.secondaryTextColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 12),
          Text(
            S.of(context).noResults,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
