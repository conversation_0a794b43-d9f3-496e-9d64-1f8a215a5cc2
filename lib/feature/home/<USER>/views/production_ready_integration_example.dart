import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/utils/auth_utils.dart';
import 'package:gather_point/core/utils/guest_reservation_handler.dart';
import 'package:gather_point/feature/home/<USER>/views/explore_list_view.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';

/// Production-ready integration example for ExploreListView
/// This demonstrates how to use all the new features in a real application
class ProductionReadyIntegrationExample extends StatefulWidget {
  const ProductionReadyIntegrationExample({super.key});

  @override
  State<ProductionReadyIntegrationExample> createState() => _ProductionReadyIntegrationExampleState();
}

class _ProductionReadyIntegrationExampleState extends State<ProductionReadyIntegrationExample> {
  @override
  void initState() {
    super.initState();
    // Initialize authentication utilities
    AuthUtils.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gather Point - Categories'),
        actions: [
          // Show user status in app bar
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: GuestReservationHandler.getUserStatusBadge(context),
          ),
        ],
      ),
      body: _buildCategoryGrid(),
    );
  }

  Widget _buildCategoryGrid() {
    final categories = [
      {'id': 1, 'title': 'شاليهات', 'icon': Icons.house, 'color': Colors.blue},
      {'id': 2, 'title': 'فلل', 'icon': Icons.villa, 'color': Colors.green},
      {'id': 3, 'title': 'شقق', 'icon': Icons.apartment, 'color': Colors.orange},
      {'id': 4, 'title': 'استراحات', 'icon': Icons.weekend, 'color': Colors.purple},
      {'id': 5, 'title': 'مخيمات', 'icon': Icons.nature, 'color': Colors.red},
      {'id': 6, 'title': 'قاعات', 'icon': Icons.meeting_room, 'color': Colors.teal},
    ];

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User greeting based on authentication status
          _buildUserGreeting(),
          const SizedBox(height: 20),
          
          // Categories grid
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
              ),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                return _buildCategoryCard(category);
              },
            ),
          ),
          
          // Quick actions for authenticated users
          if (AuthUtils.isAuthenticated()) _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildUserGreeting() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[400]!, Colors.blue[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AuthUtils.isAuthenticated() 
                      ? 'مرحباً، ${AuthUtils.getDisplayName()}'
                      : 'مرحباً بك كضيف',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  AuthUtils.isAuthenticated()
                      ? 'استكشف العقارات واستمتع بتجربة كاملة'
                      : 'سجل دخولك للحصول على تجربة أفضل',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          if (AuthUtils.isGuest())
            ElevatedButton(
              onPressed: () => _showLoginPrompt(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.blue[600],
              ),
              child: const Text('تسجيل الدخول'),
            ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => _navigateToCategory(category),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                (category['color'] as Color).withOpacity(0.8),
                category['color'] as Color,
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                category['icon'] as IconData,
                size: 48,
                color: Colors.white,
              ),
              const SizedBox(height: 12),
              Text(
                category['title'] as String,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildQuickActionButton(
            icon: Icons.favorite,
            label: 'المفضلة',
            onTap: () => _showFeatureComingSoon('المفضلة'),
          ),
          _buildQuickActionButton(
            icon: Icons.history,
            label: 'الحجوزات',
            onTap: () => _showFeatureComingSoon('الحجوزات'),
          ),
          _buildQuickActionButton(
            icon: Icons.person,
            label: 'الملف الشخصي',
            onTap: () => _showFeatureComingSoon('الملف الشخصي'),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.blue[600]),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToCategory(Map<String, dynamic> category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExploreListScreen(
          categoryId: int.tryParse(category['id']?.toString() ?? '0') ?? 0,
          categoryTitle: category['title']?.toString() ?? '',
          dioConsumer: getIt<DioConsumer>(),
        ),
      ),
    );
  }

  void _showLoginPrompt() {
    GuestReservationHandler.showFeatureUnavailableDialog(
      context: context,
      feature: 'تسجيل الدخول',
      onLoginSuccess: () {
        setState(() {}); // Refresh UI after login
      },
    );
  }

  void _showFeatureComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature قريباً!'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// Example of how to handle a specific place action with guest mode
class PlaceActionExample {
  static Future<void> handleReservation({
    required BuildContext context,
    required PlaceDetailModel place,
  }) async {
    await GuestReservationHandler.handleReservation(
      context: context,
      place: place,
      onLoginSuccess: () {
        // Handle successful login
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسجيل الدخول بنجاح! يمكنك الآن إجراء الحجز.'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      },
    );
  }

  static Future<void> handleFavorite({
    required BuildContext context,
    required PlaceDetailModel place,
    required VoidCallback onToggle,
  }) async {
    await GuestReservationHandler.handleFavorite(
      context: context,
      place: place,
      onToggleFavorite: onToggle,
      onLoginSuccess: () {
        // Handle successful login
        onToggle(); // Execute the favorite toggle after login
      },
    );
  }
}

/// Example of authentication state monitoring
class AuthStateMonitor extends StatefulWidget {
  final Widget child;

  const AuthStateMonitor({super.key, required this.child});

  @override
  State<AuthStateMonitor> createState() => _AuthStateMonitorState();
}

class _AuthStateMonitorState extends State<AuthStateMonitor> {
  @override
  void initState() {
    super.initState();
    AuthUtils.initialize();
    _checkAuthState();
  }

  void _checkAuthState() {
    // Monitor authentication state changes
    // This could be enhanced with a stream or state management solution
    if (AuthUtils.isGuest()) {
      // Handle guest state
      debugPrint('User is in guest mode');
    } else if (AuthUtils.isAuthenticated()) {
      // Handle authenticated state
      debugPrint('User is authenticated: ${AuthUtils.getDisplayName()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
