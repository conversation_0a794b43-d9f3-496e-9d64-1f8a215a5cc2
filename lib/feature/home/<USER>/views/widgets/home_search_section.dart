import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';

class HomeSearchSection extends StatefulWidget {
  const HomeSearchSection({super.key});

  @override
  _HomeSearchSectionState createState() => _HomeSearchSectionState();
}

class _HomeSearchSectionState extends State<HomeSearchSection> {
  final TextEditingController _searchController = TextEditingController();
  final DioConsumer _dioConsumer = getIt<DioConsumer>();
  bool _isLoading = false; // Track loading state

  // Extract API call logic into a separate method
  Future<List<Map<String, dynamic>>> _fetchSearchResults(String query) async {
    final response = await _dioConsumer.get(
      '/api/items/search',
      queryParameters: {
        'keyword': query,
        'page': 1,
        'limit': 20,
      },
    );

    if (response['success'] == true && response['data'] != null) {
      final data = response['data'];
      return List<Map<String, dynamic>>.from(data['items'] ?? []);
    } else {
      throw Exception('No data found');
    }
  }

  Future<void> _performSearch() async {
    final String query = _searchController.text.trim();

    if (query.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('أدخل كلمة البحث...')),
      );
      return;
    }

    setState(() {
      _isLoading = true; // Start loading
    });

    try {
      final List<Map<String, dynamic>> searchResults =
          await _fetchSearchResults(query);

      if (mounted) {
        if (searchResults.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('لا توجد نتائج بحث')),
          );
        } else {
          // Navigate to the ReelsPage with the search results and query
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ReelsPage(
                searchResults: searchResults,
                searchQuery: query,
                serviceCategoryId: 0,
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false; // Stop loading
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 64),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.lightGrey10,
        borderRadius: BorderRadius.circular(19),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'حياك ... دور علي اللي تبيه',
                hintStyle: AppTextStyles.font14Medium.copyWith(
                  color: AppColors.white,
                ),
                border: InputBorder.none,
              ),
              style: AppTextStyles.font14Medium.copyWith(
                color: AppColors.white,
              ),
              onSubmitted: (_) => _performSearch(), // Trigger search on Enter
            ),
          ),
          _isLoading
              ? const CircularProgressIndicator(
                  color: AppColors.white, // Match your app's theme
                )
              : IconButton(
                  icon: const Icon(Icons.search,
                      color: AppColors.white, size: 28),
                  onPressed: _performSearch,
                ),
        ],
      ),
    );
  }
}
