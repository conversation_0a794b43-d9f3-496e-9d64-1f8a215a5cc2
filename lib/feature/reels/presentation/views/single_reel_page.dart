import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/reels/presentation/views/widgets/video_player_widget.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:hive/hive.dart';
import 'package:dio/dio.dart';

class SingleReelPage extends StatefulWidget {
  final int selectedReelId; // The ID of the reel to display
  final bool showBackButton; // Whether to show back button

  const SingleReelPage({
    super.key,
    required this.selectedReelId,
    this.showBackButton = true,
  });

  @override
  State<SingleReelPage> createState() => _SingleReelPageState();
}

class _SingleReelPageState extends State<SingleReelPage> {
  List<Map<String, dynamic>> reelsData = [];
  bool isLoading = true;
  bool hasError = false;
  late final DioConsumer dioConsumer;
  late final PageController _pageController;
  int _currentIndex = 0;
  int _selectedReelIndex = 0;

  @override
  void initState() {
    super.initState();
    dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    
    // Initialize PageController
    _pageController = PageController(initialPage: 0);
    
    _fetchReelsAndNavigateToSelected();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Fetch all reels and find the selected one
  Future<void> _fetchReelsAndNavigateToSelected() async {
    try {
      final response = await dioConsumer.get('/api/general/reels');
      
      if (!mounted) return;
      
      if (response['data'] != null) {
        final List<Map<String, dynamic>> fetchedReels = 
            List<Map<String, dynamic>>.from(response['data']);
        
        // Find the index of the selected reel
        final selectedIndex = fetchedReels.indexWhere(
          (reel) => reel['id'] == widget.selectedReelId,
        );
        
        setState(() {
          reelsData = fetchedReels;
          isLoading = false;
          hasError = false;
          _selectedReelIndex = selectedIndex != -1 ? selectedIndex : 0;
          _currentIndex = _selectedReelIndex;
        });
        
        // Navigate to the selected reel after the widget is built
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_pageController.hasClients && selectedIndex != -1) {
            _pageController.jumpToPage(selectedIndex);
          }
        });
      } else {
        setState(() {
          isLoading = false;
          hasError = true;
        });
      }
    } catch (e) {
      debugPrint("Error fetching reels: $e");
      if (!mounted) return;
      setState(() {
        isLoading = false;
        hasError = true;
      });
    }
  }

  /// Helper method to safely parse price values that might be strings or numbers
  double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    }
    return 0.0;
  }

  Widget _buildLoading() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.yellow),
        ),
      ),
    );
  }

  Widget _buildError() {
    final theme = Theme.of(context);
    final s = S.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      color: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      child: SafeArea(
        child: Column(
          children: [
            // Back button
            if (widget.showBackButton)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            
            // Error content
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: isDark ? Colors.white70 : Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      s.failedToLoadVideo,
                      style: AppTextStyles.font16Regular.copyWith(
                        color: ThemeHelper.getPrimaryTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          isLoading = true;
                          hasError = false;
                        });
                        _fetchReelsAndNavigateToSelected();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.yellow,
                        foregroundColor: AppColors.black,
                      ),
                      child: Text(s.retry),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReels() {
    final isRTL = context.read<LocaleCubit>().isArabic();

    return SafeArea(
      child: Stack(
        children: [
          // Main Video Content
          PageView.builder(
            controller: _pageController,
            scrollDirection: Axis.vertical,
            itemCount: reelsData.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              final item = reelsData[index];
              return VideoPlayerWidget(
                facilities: item['facilities'] ?? [],
                videoUrl: item['video'],
                title: item['title'],
                location: item['title'],
                id: item['id'],
                price: _parsePrice(item['price']),
                serviceCategoryId: 2,
                favorite: item['favorite'],
                dioConsumer: dioConsumer,
              );
            },
          ),

          // Back button overlay
          if (widget.showBackButton)
            Positioned(
              top: 16,
              left: isRTL ? null : 16,
              right: isRTL ? 16 : null,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    isRTL ? Icons.arrow_forward_ios_rounded : Icons.arrow_back_ios_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                  padding: const EdgeInsets.all(8),
                ),
              ),
            ),

          // Reel indicator (optional)
          if (reelsData.length > 1)
            Positioned(
              bottom: 100,
              right: 16,
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${_currentIndex + 1}/${reelsData.length}',
                      style: AppTextStyles.font12Regular.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Set status bar based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarColor: isDark ? Colors.black : theme.scaffoldBackgroundColor,
        systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
      ),
    );

    return Scaffold(
      backgroundColor: isDark ? Colors.black : theme.scaffoldBackgroundColor,
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: isLoading
            ? _buildLoading()
            : hasError
                ? _buildError()
                : reelsData.isEmpty
                    ? _buildError() // Show error if no reels found
                    : _buildReels(),
      ),
    );
  }
}
