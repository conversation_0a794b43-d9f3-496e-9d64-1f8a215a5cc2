class User {
  int? id;
  String? fullName;
  String? username;
  dynamic bio;
  String? email;
  dynamic phone;
  int? gender;
  dynamic birthdate;
  String? referral;
  bool? otpApproved;
  bool? isGuest;
  bool? isHosterMode;
  int? userType;
  String? profilePicture;

  User({
    this.id,
    this.fullName,
    this.username,
    this.bio,
    this.email,
    this.phone,
    this.gender,
    this.birthdate,
    this.referral,
    this.otpApproved,
    this.isGuest,
    this.isHosterMode,
    this.userType,
    this.profilePicture,
  });

  @override
  String toString() {
    return 'User(id: $id, fullName: $fullName, username: $username, bio: $bio, email: $email, phone: $phone, gender: $gender, birthdate: $birthdate, referral: $referral, otpApproved: $otpApproved, is_guest: $isGuest, is_hoster_mode: $isHosterMode, user_type_id: $userType, profilePicture: $profilePicture)';
  }

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: _parseInt(json['id']),
        fullName: json['full_name']?.toString(),
        username: json['username']?.toString(),
        bio: json['bio'],
        email: json['email']?.toString(),
        phone: json['phone'],
        gender: _parseInt(json['gender']),
        birthdate: json['birthdate'],
        referral: json['referral']?.toString() ?? '',
        otpApproved: json['otp_approved'] as bool?,
        isGuest: json['is_guest'] as bool?,
        isHosterMode: json['is_hoster_mode'] as bool?,
        userType: _parseInt(json['user_type_id']),
        profilePicture: json['profile_picture']?.toString(),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'full_name': fullName,
        'username': username,
        'bio': bio,
        'email': email,
        'phone': phone,
        'gender': gender,
        'birthdate': birthdate,
        'referral': referral,
        'otp_approved': otpApproved,
        'is_guest': isGuest,
        'is_hoster_mode': isHosterMode,
        'user_type_id': userType,
        'profile_picture': profilePicture,
      };

  /// Helper method to safely parse integer fields
  static int? _parseInt(dynamic value) {
    if (value == null) return null;

    if (value is int) {
      return value;
    } else if (value is String) {
      return int.tryParse(value);
    } else if (value is double) {
      return value.toInt();
    } else {
      // Try to convert to string first, then parse
      try {
        return int.tryParse(value.toString());
      } catch (e) {
        return null;
      }
    }
  }
}
