import 'package:dartz/dartz.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/core/use_cases/use_case.dart';
import 'package:gather_point/core/use_cases/no_params.dart';
import 'package:gather_point/feature/profile/data/models/account_models.dart';
import 'package:gather_point/feature/profile/domain/repositories/profile_repository.dart';

class GetAccountInfoUseCase implements UseCase<AccountInfo, NoParams> {
  final ProfileRepository repository;

  GetAccountInfoUseCase(this.repository);

  @override
  Future<Either<Failure, AccountInfo>> call(NoParams params) async {
    return await repository.getAccountInfo();
  }
}
