import 'package:equatable/equatable.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/data/models/account_models.dart';

abstract class ProfileState extends Equatable {
  const ProfileState();

  @override
  List<Object?> get props => [];
}

class ProfileInitial extends ProfileState {}

class ProfileLoading extends ProfileState {}

class ProfileLoaded extends ProfileState {
  final UserEntity user;
  final AccountStats? accountStats;

  const ProfileLoaded(this.user, {this.accountStats});

  @override
  List<Object?> get props => [user, accountStats];
}

class ProfileUpdated extends ProfileState {
  final UserEntity user;

  const ProfileUpdated(this.user);

  @override
  List<Object?> get props => [user];
}

class ProfileHosterModeToggled extends ProfileState {
  final UserEntity user;

  const ProfileHosterModeToggled(this.user);

  @override
  List<Object?> get props => [user];
}

class ProfileLoggedOut extends ProfileState {}

class ProfileError extends ProfileState {
  final String message;

  const ProfileError(this.message);

  @override
  List<Object?> get props => [message];
}
