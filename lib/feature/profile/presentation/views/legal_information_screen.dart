import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/theme/theme_extensions.dart';
import 'package:gather_point/core/widgets/enhanced_card.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/feature/profile/data/services/legal_api_service.dart';
import 'package:gather_point/feature/profile/data/models/legal_models.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:share_plus/share_plus.dart';

class LegalInformationScreen extends StatefulWidget {
  const LegalInformationScreen({super.key});

  @override
  State<LegalInformationScreen> createState() => _LegalInformationScreenState();
}

class _LegalInformationScreenState extends State<LegalInformationScreen> {
  final LegalApiService _legalApiService = getIt<LegalApiService>();
  List<LegalDocument> _documents = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  Future<void> _loadDocuments() async {
    setState(() => _isLoading = true);
    
    try {
      final documents = await _legalApiService.getAllDocuments();
      setState(() {
        _documents = documents;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المعلومات القانونية'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildDocumentsList(),
    );
  }

  Widget _buildDocumentsList() {
    if (_documents.isEmpty) {
      return const Center(
        child: Text('لا توجد وثائق قانونية متاحة'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
      itemCount: _documents.length,
      itemBuilder: (context, index) {
        final document = _documents[index];
        return _buildDocumentCard(document);
      },
    );
  }

  Widget _buildDocumentCard(LegalDocument document) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: EnhancedCard(
        child: ListTile(
          leading: Icon(
            _getDocumentIcon(document.type),
            color: context.primaryColor,
            size: 28,
          ),
          title: Text(
            document.typeDisplay,
            style: AppTextStyles.font16SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                'الإصدار ${document.version}',
                style: AppTextStyles.font12Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
              Text(
                'آخر تحديث: ${document.updatedAt}',
                style: AppTextStyles.font12Regular.copyWith(
                  color: context.secondaryTextColor,
                ),
              ),
            ],
          ),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () => _openDocument(document),
        ),
      ),
    );
  }

  IconData _getDocumentIcon(String type) {
    switch (type) {
      case 'terms':
        return Icons.description;
      case 'privacy':
        return Icons.privacy_tip;
      case 'cookies':
        return Icons.cookie;
      case 'user_agreement':
        return Icons.person;
      case 'host_agreement':
        return Icons.home;
      default:
        return Icons.article;
    }
  }

  Future<void> _openDocument(LegalDocument document) async {
    BuildContext? dialogContext;

    try {
      print('Fetching document type: ${document.type}'); // Debug log

      // Show loading dialog and capture the dialog context
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          dialogContext = context; // Store dialog context for proper dismissal
          return const Center(
            child: CircularProgressIndicator(),
          );
        },
      );

      // Fetch full document content with timeout
      final fullDocument = await _legalApiService.getDocumentByType(document.type)
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () {
              throw Exception('انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
            },
          );

      // Close loading dialog using the stored dialog context
      if (mounted && dialogContext != null && dialogContext!.mounted) {
        Navigator.of(dialogContext!).pop(); // Close loading dialog with proper context
        dialogContext = null;

        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LegalDocumentViewScreen(document: fullDocument),
            ),
          );
        }
      } else if (mounted) {
        // Fallback: try to close any open dialog using main context
        Navigator.of(context, rootNavigator: true).pop();
      }
    } catch (e) {
      print('Error fetching document: $e'); // Debug log

      // Close loading dialog if it exists
      if (mounted && dialogContext != null && dialogContext!.mounted) {
        Navigator.of(dialogContext!).pop(); // Close loading dialog with proper context
        dialogContext = null;
      } else if (mounted) {
        // Fallback: try to close any open dialog using main context
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }
}

class LegalDocumentViewScreen extends StatelessWidget {
  final LegalDocument document;

  const LegalDocumentViewScreen({
    super.key,
    required this.document,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(document.typeDisplay),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareDocument(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 90), // Add bottom padding for nav bar (70px + 20px extra)
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Document info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: context.primaryColor,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الإصدار ${document.version}',
                          style: AppTextStyles.font14SemiBold.copyWith(
                            color: context.primaryTextColor,
                          ),
                        ),
                        Text(
                          'ساري المفعول من: ${document.effectiveDate}',
                          style: AppTextStyles.font12Regular.copyWith(
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Document content
            if (document.content != null)
              MarkdownBody(
                data: document.content!,
                styleSheet: MarkdownStyleSheet(
                  h1: AppTextStyles.font24Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                  h2: AppTextStyles.font20Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                  h3: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                  p: AppTextStyles.font14Regular.copyWith(
                    color: context.primaryTextColor,
                    height: 1.6,
                  ),
                  listBullet: AppTextStyles.font14Regular.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              )
            else
              const Center(
                child: Text('محتوى الوثيقة غير متاح'),
              ),
          ],
        ),
      ),
    );
  }

  void _shareDocument(BuildContext context) {
    final shareText = '''
${document.typeDisplay}

الإصدار: ${document.version}
ساري المفعول من: ${document.effectiveDate}

${document.content ?? 'محتوى الوثيقة غير متاح'}

---
تطبيق Gather Point
''';

    Share.share(shareText);
  }
}
