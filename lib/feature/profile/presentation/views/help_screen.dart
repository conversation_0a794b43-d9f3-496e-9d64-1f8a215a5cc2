import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedPageLayout(
      title: s.helpAndSupport,
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Quick Help Section
            _QuickHelpSection(),
            const SizedBox(height: 16),
            
            // FAQ Section
            _FAQSection(),
            const SizedBox(height: 16),
            
            // Contact Support Section
            _ContactSupportSection(),
            const SizedBox(height: 16),
            
            // Resources Section
            _ResourcesSection(),
          ],
        ),
      ),
    );
  }
}

class _QuickHelpSection extends StatelessWidget {
  const _QuickHelpSection();

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.quickHelp,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _QuickHelpCard(
                  icon: Icons.search,
                  title: s.howToSearch,
                  onTap: () {
                    // Navigate to search help
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _QuickHelpCard(
                  icon: Icons.calendar_today,
                  title: s.makeBooking,
                  onTap: () {
                    // Navigate to booking help
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _QuickHelpCard(
                  icon: Icons.home,
                  title: s.addProperty,
                  onTap: () {
                    // Navigate to listing help
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _QuickHelpCard(
                  icon: Icons.payment,
                  title: s.paymentAndBilling,
                  onTap: () {
                    // Navigate to payment help
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _QuickHelpCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _QuickHelpCard({
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: context.accentColor,
              size: 32,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: AppTextStyles.font14SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _FAQSection extends StatelessWidget {
  const _FAQSection();

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.frequentlyAskedQuestions,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _FAQItem(
            question: s.howToCancelBooking,
            answer: s.cancelBookingAnswer,
          ),
          
          _FAQItem(
            question: s.refundPolicy,
            answer: s.refundPolicyAnswer,
          ),
          
          _FAQItem(
            question: s.changeBookingInfo,
            answer: s.changeBookingAnswer,
          ),
          
          _FAQItem(
            question: s.becomeHost,
            answer: s.becomeHostAnswer,
          ),
        ],
      ),
    );
  }
}

class _FAQItem extends StatefulWidget {
  final String question;
  final String answer;

  const _FAQItem({
    required this.question,
    required this.answer,
  });

  @override
  State<_FAQItem> createState() => _FAQItemState();
}

class _FAQItemState extends State<_FAQItem> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        border: Border.all(
          color: context.secondaryTextColor.withValues(alpha: 0.2),
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ExpansionTile(
        title: Text(
          widget.question,
          style: AppTextStyles.font14SemiBold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              widget.answer,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ContactSupportSection extends StatelessWidget {
  const _ContactSupportSection();

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.contactUs,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _ContactItem(
            icon: Icons.chat_outlined,
            title: s.liveChat,
            subtitle: s.available247,
            onTap: () {
              // Open live chat
            },
          ),
          
          _ContactItem(
            icon: Icons.email_outlined,
            title: s.email,
            subtitle: '<EMAIL>',
            onTap: () async {
              final Uri emailUri = Uri(
                scheme: 'mailto',
                path: '<EMAIL>',
                query: 'subject=طلب مساعدة من تطبيق Gather Point',
              );
              if (await canLaunchUrl(emailUri)) {
                await launchUrl(emailUri);
              }
            },
          ),
          
          _ContactItem(
            icon: Icons.phone_outlined,
            title: s.phone,
            subtitle: '+966 11 234 5678',
            onTap: () async {
              final Uri phoneUri = Uri(scheme: 'tel', path: '+966112345678');
              if (await canLaunchUrl(phoneUri)) {
                await launchUrl(phoneUri);
              }
            },
          ),
        ],
      ),
    );
  }
}

class _ContactItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _ContactItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            Icon(
              icon,
              color: context.accentColor,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.font16Regular.copyWith(
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: context.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}

class _ResourcesSection extends StatelessWidget {
  const _ResourcesSection();

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            s.usefulResources,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          
          _ResourceItem(
            icon: Icons.book_outlined,
            title: s.userGuide,
            onTap: () {
              // Open user guide
            },
          ),
          
          _ResourceItem(
            icon: Icons.video_library_outlined,
            title: s.tutorialVideos,
            onTap: () {
              // Open tutorial videos
            },
          ),
          
          _ResourceItem(
            icon: Icons.article_outlined,
            title: s.helpCenter,
            onTap: () {
              // Open help center
            },
          ),
        ],
      ),
    );
  }
}

class _ResourceItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _ResourceItem({
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            Icon(
              icon,
              color: context.secondaryTextColor,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.font16Regular.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: context.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
