class ListingStatsModel {
  final int totalListings;
  final int activeListings;
  final int inactiveListings;
  final int draftListings;
  final int pendingListings;
  final int suspendedListings;
  final int totalViews;
  final int totalBookings;
  final double totalRevenue;
  final double averageRating;
  final int totalReviews;
  final int pendingReservations;
  final int activeReservations;
  final double occupancyRate;
  final double averageDailyRate;
  final double conversionRate;
  final Map<String, int> categoryBreakdown;
  final Map<String, double> monthlyRevenue;
  final Map<String, int> monthlyBookings;
  final List<TopPerformingListing> topPerformingListings;

  ListingStatsModel({
    required this.totalListings,
    required this.activeListings,
    required this.inactiveListings,
    required this.draftListings,
    required this.pendingListings,
    required this.suspendedListings,
    required this.totalViews,
    required this.totalBookings,
    required this.totalRevenue,
    required this.averageRating,
    required this.totalReviews,
    required this.pendingReservations,
    required this.activeReservations,
    required this.occupancyRate,
    required this.averageDailyRate,
    required this.conversionRate,
    required this.categoryBreakdown,
    required this.monthlyRevenue,
    required this.monthlyBookings,
    required this.topPerformingListings,
  });

  factory ListingStatsModel.fromJson(Map<String, dynamic> json) {
    return ListingStatsModel(
      totalListings: _parseInt(json['total_listings']) ?? 0,
      activeListings: _parseInt(json['active_listings']) ?? 0,
      inactiveListings: _parseInt(json['inactive_listings']) ?? 0,
      draftListings: _parseInt(json['draft_listings']) ?? 0,
      pendingListings: _parseInt(json['pending_listings']) ?? 0,
      suspendedListings: _parseInt(json['suspended_listings']) ?? 0,
      totalViews: _parseInt(json['total_views']) ?? 0,
      totalBookings: _parseInt(json['total_bookings']) ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0).toDouble(),
      averageRating: (json['average_rating'] ?? 0).toDouble(),
      totalReviews: _parseInt(json['total_reviews']) ?? 0,
      pendingReservations: _parseInt(json['pending_reservations']) ?? 0,
      activeReservations: _parseInt(json['active_reservations']) ?? 0,
      occupancyRate: (json['occupancy_rate'] ?? 0).toDouble(),
      averageDailyRate: (json['average_daily_rate'] ?? 0).toDouble(),
      conversionRate: (json['conversion_rate'] ?? 0).toDouble(),
      categoryBreakdown: Map<String, int>.from(json['category_breakdown'] ?? {}),
      monthlyRevenue: Map<String, double>.from(
        (json['monthly_revenue'] ?? {}).map((k, v) => MapEntry(k, v.toDouble())),
      ),
      monthlyBookings: Map<String, int>.from(json['monthly_bookings'] ?? {}),
      topPerformingListings: (json['top_performing_listings'] as List<dynamic>? ?? [])
          .map((item) => TopPerformingListing.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_listings': totalListings,
      'active_listings': activeListings,
      'inactive_listings': inactiveListings,
      'draft_listings': draftListings,
      'pending_listings': pendingListings,
      'suspended_listings': suspendedListings,
      'total_views': totalViews,
      'total_bookings': totalBookings,
      'total_revenue': totalRevenue,
      'average_rating': averageRating,
      'total_reviews': totalReviews,
      'pending_reservations': pendingReservations,
      'active_reservations': activeReservations,
      'occupancy_rate': occupancyRate,
      'average_daily_rate': averageDailyRate,
      'conversion_rate': conversionRate,
      'category_breakdown': categoryBreakdown,
      'monthly_revenue': monthlyRevenue,
      'monthly_bookings': monthlyBookings,
      'top_performing_listings': topPerformingListings.map((item) => item.toJson()).toList(),
    };
  }

  /// Helper method to safely parse integer fields
  static int? _parseInt(dynamic value) {
    if (value == null) return null;

    if (value is int) {
      return value;
    } else if (value is String) {
      return int.tryParse(value);
    } else if (value is double) {
      return value.toInt();
    } else {
      // Try to convert to string first, then parse
      try {
        return int.tryParse(value.toString());
      } catch (e) {
        return null;
      }
    }
  }

  // Helper getters
  String get totalRevenueDisplay {
    if (totalRevenue >= 1000000) {
      return '${(totalRevenue / 1000000).toStringAsFixed(1)}م ر.س';
    } else if (totalRevenue >= 1000) {
      return '${(totalRevenue / 1000).toStringAsFixed(1)}ك ر.س';
    } else {
      return '${totalRevenue.toStringAsFixed(0)} ر.س';
    }
  }

  String get averageRatingDisplay {
    if (averageRating == 0) return 'لا توجد تقييمات';
    return '${averageRating.toStringAsFixed(1)} ⭐';
  }

  String get occupancyRateDisplay {
    return '${(occupancyRate * 100).toStringAsFixed(1)}%';
  }

  String get conversionRateDisplay {
    return '${(conversionRate * 100).toStringAsFixed(1)}%';
  }

  String get averageDailyRateDisplay {
    return '${averageDailyRate.toStringAsFixed(0)} ر.س';
  }

  double get publishedListingsPercentage {
    if (totalListings == 0) return 0;
    return (activeListings + inactiveListings) / totalListings;
  }

  double get activeListingsPercentage {
    if (totalListings == 0) return 0;
    return activeListings / totalListings;
  }

  bool get hasListings => totalListings > 0;
  bool get hasActiveListings => activeListings > 0;
  bool get hasPendingListings => pendingListings > 0;
  bool get hasRevenue => totalRevenue > 0;
  bool get hasBookings => totalBookings > 0;

  @override
  String toString() {
    return 'ListingStatsModel(totalListings: $totalListings, activeListings: $activeListings, totalRevenue: $totalRevenue)';
  }
}

class TopPerformingListing {
  final int id;
  final String title;
  final String? imageUrl;
  final double revenue;
  final int bookings;
  final int views;
  final double rating;
  final double conversionRate;

  TopPerformingListing({
    required this.id,
    required this.title,
    this.imageUrl,
    required this.revenue,
    required this.bookings,
    required this.views,
    required this.rating,
    required this.conversionRate,
  });

  factory TopPerformingListing.fromJson(Map<String, dynamic> json) {
    return TopPerformingListing(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      imageUrl: json['image_url'],
      revenue: (json['revenue'] ?? 0).toDouble(),
      bookings: json['bookings'] ?? 0,
      views: json['views'] ?? 0,
      rating: (json['rating'] ?? 0).toDouble(),
      conversionRate: (json['conversion_rate'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'image_url': imageUrl,
      'revenue': revenue,
      'bookings': bookings,
      'views': views,
      'rating': rating,
      'conversion_rate': conversionRate,
    };
  }

  String get revenueDisplay {
    if (revenue >= 1000000) {
      return '${(revenue / 1000000).toStringAsFixed(1)}م ر.س';
    } else if (revenue >= 1000) {
      return '${(revenue / 1000).toStringAsFixed(1)}ك ر.س';
    } else {
      return '${revenue.toStringAsFixed(0)} ر.س';
    }
  }

  String get ratingDisplay {
    if (rating == 0) return 'لا توجد تقييمات';
    return '${rating.toStringAsFixed(1)} ⭐';
  }

  String get conversionRateDisplay {
    return '${(conversionRate * 100).toStringAsFixed(1)}%';
  }

  String get performanceDisplay {
    final parts = <String>[];
    if (views > 0) parts.add('$views مشاهدة');
    if (bookings > 0) parts.add('$bookings حجز');
    return parts.join(' • ');
  }

  @override
  String toString() {
    return 'TopPerformingListing(id: $id, title: $title, revenue: $revenue)';
  }
}
