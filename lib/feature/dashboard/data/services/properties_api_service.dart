import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/feature/host/data/models/reservation_model.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/home/<USER>/models/place_detail_model.dart';
import 'package:gather_point/feature/host/data/models/property_type_model.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';

class PropertiesApiService {
  final DioConsumer _dioConsumer;

  PropertiesApiService(this._dioConsumer);

  /// Get place details by ID
  Future<PlaceDetailModel> getPlaceDetails(int placeId) async {
    try {
      final response = await _dioConsumer.get('${EndPoints.itemsDetail}/$placeId');

      if (response['status'] == true) {
        return PlaceDetailModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch place details');
      }
    } catch (e) {
      throw Exception('Failed to fetch place details: ${e.toString()}');
    }
  }

  /// Get service categories for property creation
  Future<List<ServiceCategory>> getServiceCategories() async {
    try {
      debugPrint('🔄 Fetching service categories from: ${EndPoints.serviceCategoriesList}');
      final response = await _dioConsumer.get(EndPoints.serviceCategoriesList);

      debugPrint('📥 Categories API Response: $response');

      // Categories API returns different format - check both success and status
      if (response['success'] == true || response['status'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        debugPrint('✅ Categories data found: ${data.length} items');

        final categories = data.map((item) {
          try {
            return ServiceCategory.fromJson(item);
          } catch (e) {
            debugPrint('❌ Error parsing category item: $item, Error: $e');
            rethrow;
          }
        }).toList();

        debugPrint('✅ Successfully parsed ${categories.length} categories');
        return categories;
      } else {
        final errorMsg = response['message'] ?? 'Failed to fetch categories';
        debugPrint('❌ Categories API failed: $errorMsg');
        throw Exception(errorMsg);
      }
    } catch (e) {
      debugPrint('❌ Categories API exception: ${e.toString()}');
      throw Exception('Failed to fetch categories: ${e.toString()}');
    }
  }

  /// Get facilities list
  Future<List<FacilityModel>> getFacilities({int? serviceCategoryId, int? facilitiesCategoryId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (serviceCategoryId != null) {
        queryParams['service_category_id'] = serviceCategoryId;
      }
      if (facilitiesCategoryId != null) {
        queryParams['facilities_category_id'] = facilitiesCategoryId;
      }

      debugPrint('🔄 Fetching facilities from: ${EndPoints.facilitiesList}');
      final response = await _dioConsumer.get(
        EndPoints.facilitiesList,
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      debugPrint('📥 Facilities API Response: $response');

      if (response['success'] == true || response['status'] == true) {
        final dynamic data = response['data'];
        debugPrint('✅ Facilities data found: $data');

        // Handle both flat list and grouped response
        if (data is List) {
          // Flat list response (when filtering by specific category)
          final facilities = data.map((item) {
            try {
              return FacilityModel.fromJson(item);
            } catch (e) {
              debugPrint('❌ Error parsing facility item: $item, Error: $e');
              rethrow;
            }
          }).toList();

          debugPrint('✅ Successfully parsed ${facilities.length} facilities');
          return facilities;
        } else if (data is List && data.isNotEmpty && data.first is Map && data.first.containsKey('facilities')) {
          // Grouped response - flatten all facilities from all categories
          final List<FacilityModel> allFacilities = [];
          for (final categoryGroup in data) {
            final List<dynamic> categoryFacilities = categoryGroup['facilities'] ?? [];
            for (final facilityItem in categoryFacilities) {
              try {
                allFacilities.add(FacilityModel.fromJson(facilityItem));
              } catch (e) {
                debugPrint('❌ Error parsing facility item: $facilityItem, Error: $e');
                rethrow;
              }
            }
          }
          debugPrint('✅ Successfully parsed ${allFacilities.length} facilities from grouped response');
          return allFacilities;
        } else {
          debugPrint('✅ Empty facilities data');
          return [];
        }
      } else {
        final errorMsg = response['message'] ?? 'Failed to fetch facilities';
        debugPrint('❌ Facilities API failed: $errorMsg');
        throw Exception(errorMsg);
      }
    } catch (e) {
      debugPrint('❌ Facilities API exception: ${e.toString()}');
      throw Exception('Failed to fetch facilities: ${e.toString()}');
    }
  }

  /// Get facilities grouped by category
  Future<Map<String, List<FacilityModel>>> getFacilitiesGroupedByCategory({int? serviceCategoryId}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (serviceCategoryId != null) {
        queryParams['service_category_id'] = serviceCategoryId;
      }

      debugPrint('🔄 Fetching grouped facilities from: ${EndPoints.facilitiesList}');
      final response = await _dioConsumer.get(
        EndPoints.facilitiesList,
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      debugPrint('📥 Grouped Facilities API Response: $response');

      if (response['success'] == true || response['status'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        debugPrint('✅ Grouped facilities data found: ${data.length} categories');

        final Map<String, List<FacilityModel>> groupedFacilities = {};

        for (final categoryGroup in data) {
          final String categoryNameEn = categoryGroup['category_name_en'] ?? 'Uncategorized';
          final String categoryNameAr = categoryGroup['category_name_ar'] ?? 'غير مصنف';
          final List<dynamic> categoryFacilities = categoryGroup['facilities'] ?? [];

          // Use Arabic name if current locale is Arabic, otherwise use English
          final String categoryName = categoryNameAr.isNotEmpty ? categoryNameAr : categoryNameEn;

          final List<FacilityModel> facilities = categoryFacilities.map((item) {
            try {
              return FacilityModel.fromJson(item);
            } catch (e) {
              debugPrint('❌ Error parsing facility item: $item, Error: $e');
              rethrow;
            }
          }).toList();

          groupedFacilities[categoryName] = facilities;
        }

        debugPrint('✅ Successfully parsed facilities into ${groupedFacilities.length} categories');
        return groupedFacilities;
      } else {
        final errorMsg = response['message'] ?? 'Failed to fetch facilities';
        debugPrint('❌ Grouped Facilities API failed: $errorMsg');
        throw Exception(errorMsg);
      }
    } catch (e) {
      debugPrint('❌ Grouped Facilities API exception: ${e.toString()}');
      throw Exception('Failed to fetch facilities: ${e.toString()}');
    }
  }

  /// Get property types list
  Future<List<PropertyTypeModel>> getPropertyTypes() async {
    try {
      debugPrint('🔄 Fetching property types from: ${EndPoints.propertyTypesList}');
      final response = await _dioConsumer.get(EndPoints.propertyTypesList);

      debugPrint('📥 Property Types API Response: $response');

      if (response['success'] == true || response['status'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        debugPrint('✅ Property types data found: ${data.length} items');

        final propertyTypes = data.map((item) {
          try {
            return PropertyTypeModel.fromJson(item);
          } catch (e) {
            debugPrint('❌ Error parsing property type item: $item, Error: $e');
            rethrow;
          }
        }).toList();

        debugPrint('✅ Successfully parsed ${propertyTypes.length} property types');
        return propertyTypes;
      } else {
        final errorMsg = response['message'] ?? 'Failed to fetch property types';
        debugPrint('❌ Property Types API failed: $errorMsg');
        throw Exception(errorMsg);
      }
    } catch (e) {
      debugPrint('❌ Property Types API exception: ${e.toString()}');
      throw Exception('Failed to fetch property types: ${e.toString()}');
    }
  }

  /// Get cancellation policies list
  Future<List<CancellationPolicyModel>> getCancellationPolicies() async {
    try {
      debugPrint('🔄 Fetching cancellation policies from: ${EndPoints.cancellationPoliciesList}');
      final response = await _dioConsumer.get(EndPoints.cancellationPoliciesList);

      debugPrint('📥 Cancellation Policies API Response: $response');

      if (response['success'] == true || response['status'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        debugPrint('✅ Cancellation policies data found: ${data.length} items');

        final policies = data.map((item) {
          try {
            return CancellationPolicyModel.fromJson(item);
          } catch (e) {
            debugPrint('❌ Error parsing cancellation policy item: $item, Error: $e');
            rethrow;
          }
        }).toList();

        debugPrint('✅ Successfully parsed ${policies.length} cancellation policies');
        return policies;
      } else {
        final errorMsg = response['message'] ?? 'Failed to fetch cancellation policies';
        debugPrint('❌ Cancellation Policies API failed: $errorMsg');
        throw Exception(errorMsg);
      }
    } catch (e) {
      debugPrint('❌ Cancellation Policies API exception: ${e.toString()}');
      throw Exception('Failed to fetch cancellation policies: ${e.toString()}');
    }
  }

  /// Create a new property
  Future<PropertyItemModel> createProperty({
    required String title,
    required String content,
    required int serviceCategoryId,
    required double price,
    double? weekendPrice,
    double? weekPrice,
    double? monthPrice,
    double? lat,
    double? lon,
    String? address,
    int? noGuests,
    int? beds,
    int? baths,
    String? bookingRules,
    String? cancelationRules,
    String? tourismPermitNumber,
    int? propertyTypeId,
    int? shortTermPolicyId,
    int? longTermPolicyId,
    bool? requiresConfirmation,
    List<int>? facilityIds,
    File? mainImage,
    File? video,
    List<File>? galleryImages,
    File? tourismPermitDocument,
  }) async {
    try {
      final formData = <String, dynamic>{
        'title': title,
        'content': content,
        'service_category_id': serviceCategoryId,
        'price': price,
        if (weekendPrice != null) 'weekend_price': weekendPrice,
        if (weekPrice != null) 'week_price': weekPrice,
        if (monthPrice != null) 'month_price': monthPrice,
        if (lat != null) 'lat': lat,
        if (lon != null) 'lon': lon,
        if (address != null) 'address': address,
        if (noGuests != null) 'no_guests': noGuests,
        if (beds != null) 'beds': beds,
        if (baths != null) 'baths': baths,
        if (bookingRules != null) 'booking_rules': bookingRules,
        if (cancelationRules != null) 'cancelation_rules': cancelationRules,
        if (tourismPermitNumber != null) 'tourism_permit_number': tourismPermitNumber,
        if (propertyTypeId != null) 'property_type_id': propertyTypeId,
        if (shortTermPolicyId != null) 'short_term_policy_id': shortTermPolicyId,
        if (longTermPolicyId != null) 'long_term_policy_id': longTermPolicyId,
        if (requiresConfirmation != null) 'confirmation': requiresConfirmation ? 1 : 0,
      };

      // Add facilities as array - Laravel expects facilities[] format for FormData
      if (facilityIds != null && facilityIds.isNotEmpty) {
        debugPrint('🏗️ Adding facilities to form data: $facilityIds (type: ${facilityIds.runtimeType})');

        // For FormData, Laravel expects array fields to be sent as facilities[0], facilities[1], etc.
        for (int i = 0; i < facilityIds.length; i++) {
          formData['facilities[$i]'] = facilityIds[i];
        }
      } else {
        debugPrint('⚠️ No facilities provided or empty list');
        // Send empty array to satisfy Laravel validation
        formData['facilities'] = [];
      }

      // Add main image if provided
      if (mainImage != null) {
        formData['image'] = await MultipartFile.fromFile(
          mainImage.path,
          filename: mainImage.path.split('/').last,
        );
      }

      // Add video if provided
      if (video != null) {
        formData['video'] = await MultipartFile.fromFile(
          video.path,
          filename: video.path.split('/').last,
        );
      }

      // Add gallery images if provided
      if (galleryImages != null && galleryImages.isNotEmpty) {
        for (int i = 0; i < galleryImages.length; i++) {
          formData['gallery[$i]'] = await MultipartFile.fromFile(
            galleryImages[i].path,
            filename: galleryImages[i].path.split('/').last,
          );
        }
      }

      // Add tourism permit document if provided
      if (tourismPermitDocument != null) {
        formData['tourism_permit_document'] = await MultipartFile.fromFile(
          tourismPermitDocument.path,
          filename: tourismPermitDocument.path.split('/').last,
        );
      }

      // Debug: Print complete form data before sending
      debugPrint('🚀 Complete form data being sent to API:');
      formData.forEach((key, value) {
        if (value is MultipartFile) {
          debugPrint('  $key: MultipartFile(${value.filename})');
        } else {
          debugPrint('  $key: $value (${value.runtimeType})');
        }
      });

      // Specifically highlight confirmation field
      if (formData.containsKey('confirmation')) {
        debugPrint('✅ Confirmation field included: ${formData['confirmation']} (type: ${formData['confirmation'].runtimeType})');
      } else {
        debugPrint('❌ Confirmation field missing from form data');
      }

      final response = await _dioConsumer.post(
        EndPoints.createProperty,
        data: formData,
        isFormData: true,
      );

      if (response['success'] == true) {
        return PropertyItemModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to create property');
      }
    } catch (e) {
      throw Exception('Failed to create property: ${e.toString()}');
    }
  }

  /// Update an existing property
  Future<PropertyItemModel> updateProperty({
    required int propertyId,
    String? title,
    String? content,
    int? serviceCategoryId,
    double? price,
    double? weekendPrice,
    double? weekPrice,
    double? monthPrice,
    double? lat,
    double? lon,
    String? address,
    int? noGuests,
    int? beds,
    int? baths,
    String? bookingRules,
    String? cancelationRules,
    int? propertyTypeId,
    int? shortTermPolicyId,
    int? longTermPolicyId,
    bool? requiresConfirmation,
    List<int>? facilityIds,
    File? mainImage,
    File? video,
  }) async {
    try {
      final formData = <String, dynamic>{};

      if (title != null) formData['title'] = title;
      if (content != null) formData['content'] = content;
      if (serviceCategoryId != null) formData['service_category_id'] = serviceCategoryId;
      if (price != null) formData['price'] = price;
      if (weekendPrice != null) formData['weekend_price'] = weekendPrice;
      if (weekPrice != null) formData['week_price'] = weekPrice;
      if (monthPrice != null) formData['month_price'] = monthPrice;
      if (lat != null) formData['lat'] = lat;
      if (lon != null) formData['lon'] = lon;
      if (address != null) formData['address'] = address;
      if (noGuests != null) formData['no_guests'] = noGuests;
      if (beds != null) formData['beds'] = beds;
      if (baths != null) formData['baths'] = baths;
      if (bookingRules != null) formData['booking_rules'] = bookingRules;
      if (cancelationRules != null) formData['cancelation_rules'] = cancelationRules;
      if (propertyTypeId != null) formData['property_type_id'] = propertyTypeId;
      if (shortTermPolicyId != null) formData['short_term_policy_id'] = shortTermPolicyId;
      if (longTermPolicyId != null) formData['long_term_policy_id'] = longTermPolicyId;
      if (requiresConfirmation != null) formData['confirmation'] = requiresConfirmation;
      if (facilityIds != null && facilityIds.isNotEmpty) formData['facilities'] = facilityIds;

      // Add main image if provided
      if (mainImage != null) {
        formData['image'] = await MultipartFile.fromFile(
          mainImage.path,
          filename: mainImage.path.split('/').last,
        );
      }

      // Add video if provided
      if (video != null) {
        formData['video'] = await MultipartFile.fromFile(
          video.path,
          filename: video.path.split('/').last,
        );
      }

      final response = await _dioConsumer.put(
        '${EndPoints.itemsUpdate}/$propertyId',
        data: formData,
        isFormData: true,
      );

      if (response['success'] == true) {
        return PropertyItemModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update property');
      }
    } catch (e) {
      throw Exception('Failed to update property: ${e.toString()}');
    }
  }

  /// Upload gallery images for a property
  Future<bool> uploadGalleryImages(int propertyId, List<File> images) async {
    try {
      final formData = <String, dynamic>{};

      for (int i = 0; i < images.length; i++) {
        formData['images[$i]'] = await MultipartFile.fromFile(
          images[i].path,
          filename: images[i].path.split('/').last,
        );
      }

      final response = await _dioConsumer.post(
        '${EndPoints.itemsUploadGallery}/$propertyId',
        data: formData,
        isFormData: true,
      );

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to upload gallery images: ${e.toString()}');
    }
  }

  /// Get user's properties
  Future<List<PropertyItemModel>> getUserProperties({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        'user_properties': true, // To get only current user's properties
      };

      final response = await _dioConsumer.get(
        EndPoints.itemsList,
        queryParameters: queryParams,
      );

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((item) => PropertyItemModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch properties');
      }
    } catch (e) {
      throw Exception('Failed to fetch properties: ${e.toString()}');
    }
  }

  /// Delete a property
  Future<bool> deleteProperty(int propertyId) async {
    try {
      final response = await _dioConsumer.delete('/api/items/$propertyId');
      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to delete property: ${e.toString()}');
    }
  }

  /// Toggle property active status
  Future<bool> togglePropertyStatus(int propertyId, bool active) async {
    try {
      final response = await _dioConsumer.patch(
        '/api/items/$propertyId/status',
        data: {'active': active},
      );
      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to update property status: ${e.toString()}');
    }
  }
}

class FacilityModel {
  final int id;
  final String title;
  final String? icon;
  final int order;

  const FacilityModel({
    required this.id,
    required this.title,
    this.icon,
    required this.order,
  });

  factory FacilityModel.fromJson(Map<String, dynamic> json) {
    return FacilityModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      icon: json['icon'],
      order: json['order'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'icon': icon,
      'order': order,
    };
  }
}
