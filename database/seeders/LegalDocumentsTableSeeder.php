<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\LegalDocument;
use Carbon\Carbon;

class LegalDocumentsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();
        LegalDocument::create([
            'type' => 'terms',
            'title_en' => 'Terms of Service',
            'title_ar' => 'شروط الخدمة',
            'content_en' => 'These are the terms of service.',
            'content_ar' => 'هذه هي شروط الخدمة.',
            'version' => '1.0',
            'effective_date' => $now,
            'is_active' => true,
        ]);
        LegalDocument::create([
            'type' => 'privacy',
            'title_en' => 'Privacy Policy',
            'title_ar' => 'سياسة الخصوصية',
            'content_en' => 'This is the privacy policy.',
            'content_ar' => 'هذه هي سياسة الخصوصية.',
            'version' => '1.0',
            'effective_date' => $now,
            'is_active' => true,
        ]);
        LegalDocument::create([
            'type' => 'cookies',
            'title_en' => 'Cookie Policy',
            'title_ar' => 'سياسة الكوكيز',
            'content_en' => 'This is the cookie policy.',
            'content_ar' => 'هذه هي سياسة الكوكيز.',
            'version' => '1.0',
            'effective_date' => $now,
            'is_active' => true,
        ]);
    }
}
