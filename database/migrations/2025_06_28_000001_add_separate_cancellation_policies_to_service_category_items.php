<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            // Add separate policy fields
            $table->unsignedBigInteger('short_term_policy_id')->nullable()->after('cancelation_policy_id');
            $table->unsignedBigInteger('long_term_policy_id')->nullable()->after('short_term_policy_id');
            
            // Add foreign key constraints
            $table->foreign('short_term_policy_id')
                  ->references('id')
                  ->on('cancellation_policies')
                  ->onDelete('set null')
                  ->name('sci_short_term_policy_fk');
                  
            $table->foreign('long_term_policy_id')
                  ->references('id')
                  ->on('cancellation_policies')
                  ->onDelete('set null')
                  ->name('sci_long_term_policy_fk');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_category_items', function (Blueprint $table) {
            $table->dropForeign('sci_short_term_policy_fk');
            $table->dropForeign('sci_long_term_policy_fk');
            $table->dropColumn(['short_term_policy_id', 'long_term_policy_id']);
        });
    }
};
