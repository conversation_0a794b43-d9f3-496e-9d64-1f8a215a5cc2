<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\PropertyTypesResource;
use App\Models\Admin\PropertyType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PropertyTypesController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get property types list.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function get_list(Request $request)
    {
        $list = PropertyType::orderBy('order')->get();

        // Return standardized JSON response
        return response()->json([
            'success' => !empty($list),
            'message' => trans('api.' . (!empty($list) ? 'data_retrieved_success' : 'no_data_found')),
            'data' => PropertyTypesResource::collection($list),
        ], !empty($list) ? 200 : 404);
    }
}
