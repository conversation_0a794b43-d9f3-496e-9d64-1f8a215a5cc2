<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\FacilitiesResource;
use App\Models\Admin\Facilities;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FacilitiesController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get facilities list.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function get_list(Request $request)
    {
        $request->validate([
            'service_category_id' => 'nullable|integer|exists:service_categories,id',
            'facilities_category_id' => 'nullable|integer|exists:facilities_categories,id',
        ]);

        $query = Facilities::with('category')->orderBy('order');

        // Filter by service category if provided
        // This would require a relationship between service categories and facilities
        // For now, we'll implement basic filtering by facilities category
        if ($request->has('facilities_category_id')) {
            $query->where('facilities_category_id', $request->facilities_category_id);
        }

        $list = $query->get();

        // Group facilities by category if no specific category filter is applied
        if (!$request->has('facilities_category_id')) {
            $groupedFacilities = $list->groupBy('facilities_category_id');
            $result = [];

            foreach ($groupedFacilities as $categoryId => $facilities) {
                $category = $facilities->first()->category;
                $result[] = [
                    'category_id' => $categoryId,
                    'category_name_en' => $category ? $category->title_en : 'Uncategorized',
                    'category_name_ar' => $category ? $category->title_ar : 'غير مصنف',
                    'facilities' => FacilitiesResource::collection($facilities),
                ];
            }

            return response()->json([
                'success' => !empty($result),
                'message' => trans('api.' . (!empty($result) ? 'data_retrieved_success' : 'no_data_found')),
                'data' => $result,
            ], !empty($result) ? 200 : 404);
        }

        // Return flat list if specific category is requested
        return response()->json([
            'success' => !empty($list),
            'message' => trans('api.' . (!empty($list) ? 'data_retrieved_success' : 'no_data_found')),
            'data' => FacilitiesResource::collection($list),
        ], !empty($list) ? 200 : 404);
    }
}
