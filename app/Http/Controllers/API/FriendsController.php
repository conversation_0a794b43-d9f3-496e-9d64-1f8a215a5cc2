<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Friendship;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class FriendsController extends Controller
{
    /**
     * Get user's friends list
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();
            $friends = $user->friends();

            $friendsData = $friends->map(function ($friend) use ($user) {
                $friendship = Friendship::where(function ($query) use ($user, $friend) {
                    $query->where('user_id', $user->id)->where('friend_id', $friend->id);
                })->orWhere(function ($query) use ($user, $friend) {
                    $query->where('user_id', $friend->id)->where('friend_id', $user->id);
                })->first();

                return [
                    'id' => $friend->id,
                    'full_name' => $friend->full_name,
                    'email' => $friend->email,
                    'phone' => $friend->phone,
                    'profile_photo_url' => $friend->profile_photo_url,
                    'is_hoster' => $friend->user_type_id == 2, // Assuming 2 is hoster type
                    'friends_since' => $friendship ? $friendship->accepted_at->format('Y-m-d') : null,
                    'mutual_friends_count' => $this->getMutualFriendsCount($user, $friend),
                ];
            });

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $friendsData->values()->toArray(),
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Get pending friend requests (received)
     */
    public function pendingRequests(Request $request)
    {
        try {
            $user = Auth::user();
            $pendingRequests = $user->pendingReceivedRequests()->get();

            $requestsData = $pendingRequests->map(function ($friendship) {
                $requester = $friendship->user;
                return [
                    'friendship_id' => $friendship->id,
                    'user' => [
                        'id' => $requester->id,
                        'full_name' => $requester->full_name,
                        'email' => $requester->email,
                        'phone' => $requester->phone,
                        'profile_photo_url' => $requester->profile_photo_url,
                        'is_hoster' => $requester->user_type_id == 2,
                    ],
                    'requested_at' => $friendship->created_at->format('Y-m-d H:i:s'),
                    'mutual_friends_count' => $this->getMutualFriendsCount(Auth::user(), $requester),
                ];
            });

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $requestsData->toArray(),
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Send friend request
     */
    public function sendRequest(Request $request)
    {
        try {
            $request->validate([
                'friend_id' => 'required|integer|exists:users,id',
            ]);

            $user = Auth::user();
            $friendId = $request->friend_id;

            // Can't send request to yourself
            if ($user->id == $friendId) {
                return $this->ApiResponse(
                    false,
                    'لا يمكنك إرسال طلب صداقة لنفسك',
                    [],
                    400
                );
            }

            // Check if friendship already exists
            $existingFriendship = Friendship::where(function ($query) use ($user, $friendId) {
                $query->where('user_id', $user->id)->where('friend_id', $friendId);
            })->orWhere(function ($query) use ($user, $friendId) {
                $query->where('user_id', $friendId)->where('friend_id', $user->id);
            })->first();

            if ($existingFriendship) {
                if ($existingFriendship->status == 'accepted') {
                    return $this->ApiResponse(
                        false,
                        'أنتما أصدقاء بالفعل',
                        [],
                        400
                    );
                } elseif ($existingFriendship->status == 'pending') {
                    return $this->ApiResponse(
                        false,
                        'طلب الصداقة معلق بالفعل',
                        [],
                        400
                    );
                }
            }

            $friendship = Friendship::create([
                'user_id' => $user->id,
                'friend_id' => $friendId,
                'status' => 'pending',
            ]);

            return $this->ApiResponse(
                true,
                'تم إرسال طلب الصداقة بنجاح',
                ['friendship_id' => $friendship->id],
                201
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Accept friend request
     */
    public function acceptRequest(Request $request)
    {
        try {
            $request->validate([
                'friendship_id' => 'required|integer|exists:friendships,id',
            ]);

            $user = Auth::user();
            $friendship = Friendship::find($request->friendship_id);

            // Check if user is the recipient of this request
            if ($friendship->friend_id != $user->id) {
                return $this->ApiResponse(
                    false,
                    'غير مصرح لك بقبول هذا الطلب',
                    [],
                    403
                );
            }

            if ($friendship->status != 'pending') {
                return $this->ApiResponse(
                    false,
                    'هذا الطلب ليس معلقاً',
                    [],
                    400
                );
            }

            $friendship->accept();

            return $this->ApiResponse(
                true,
                'تم قبول طلب الصداقة بنجاح',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Decline friend request
     */
    public function declineRequest(Request $request)
    {
        try {
            $request->validate([
                'friendship_id' => 'required|integer|exists:friendships,id',
            ]);

            $user = Auth::user();
            $friendship = Friendship::find($request->friendship_id);

            // Check if user is the recipient of this request
            if ($friendship->friend_id != $user->id) {
                return $this->ApiResponse(
                    false,
                    'غير مصرح لك برفض هذا الطلب',
                    [],
                    403
                );
            }

            $friendship->decline();

            return $this->ApiResponse(
                true,
                'تم رفض طلب الصداقة',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Remove friend
     */
    public function removeFriend(Request $request)
    {
        try {
            $request->validate([
                'friend_id' => 'required|integer|exists:users,id',
            ]);

            $user = Auth::user();
            $friendId = $request->friend_id;

            $friendship = Friendship::where(function ($query) use ($user, $friendId) {
                $query->where('user_id', $user->id)->where('friend_id', $friendId);
            })->orWhere(function ($query) use ($user, $friendId) {
                $query->where('user_id', $friendId)->where('friend_id', $user->id);
            })->where('status', 'accepted')->first();

            if (!$friendship) {
                return $this->ApiResponse(
                    false,
                    'لستما أصدقاء',
                    [],
                    400
                );
            }

            $friendship->delete();

            return $this->ApiResponse(
                true,
                'تم حذف الصديق بنجاح',
                [],
                200
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_wrong'),
                [],
                500
            );
        }
    }

    /**
     * Search for users to add as friends
     */
    public function searchUsers(Request $request)
    {
        try {
            $request->validate([
                'query' => 'required|string|min:2',
            ]);

            $user = Auth::user();
            $query = $request->query('query');

            $users = User::where('id', '!=', $user->id)
                ->where(function ($q) use ($query) {
                    $q->where('name', 'LIKE', "%{$query}%")
                      ->orWhere('email', 'LIKE', "%{$query}%")
                      ->orWhere('phone', 'LIKE', "%{$query}%");
                })
                ->limit(20)
                ->get();

            $usersData = $users->map(function ($searchUser) use ($user) {
                $friendshipStatus = 'none';
                
                if ($user->isFriendsWith($searchUser->id)) {
                    $friendshipStatus = 'friends';
                } elseif ($user->hasSentFriendRequestTo($searchUser->id)) {
                    $friendshipStatus = 'request_sent';
                } elseif ($user->hasReceivedFriendRequestFrom($searchUser->id)) {
                    $friendshipStatus = 'request_received';
                }

                return [
                    'id' => $searchUser->id,
                    'full_name' => $searchUser->name, // Use 'name' for full_name in response
                    'email' => $searchUser->email,
                    'phone' => $searchUser->phone,
                    'profile_photo_url' => $searchUser->profile_photo_url,
                    'is_hoster' => $searchUser->user_type_id == 2,
                    'friendship_status' => $friendshipStatus,
                    'mutual_friends_count' => $this->getMutualFriendsCount($user, $searchUser),
                ];
            });

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                $usersData->toArray(),
                200
            );
        } catch (\Exception $e) {
            // Debug: return the real error message for troubleshooting
            return $this->ApiResponse(
                false,
                $e->getMessage(),
                [],
                500
            );
        }
    }

    /**
     * Get mutual friends count between two users
     */
    private function getMutualFriendsCount($user1, $user2)
    {
        $user1Friends = $user1->friends()->pluck('id');
        $user2Friends = $user2->friends()->pluck('id');
        
        return $user1Friends->intersect($user2Friends)->count();
    }
}
