<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class AccountController extends Controller
{
    /**
     * Get account information.
     */
    public function getAccountInfo(): JsonResponse
    {
        try {
            $user = Auth::user();

            // Calculate average rating for the user
            $averageRating = 0;
            $totalReviews = 0;

            if ($user->isHosterMode) {
                // For hosters, get average rating from reviews on their properties
                $properties = $user->serviceCategoryItems()->pluck('id');
                if ($properties->isNotEmpty()) {
                    $reviews = \App\Models\Review::whereIn('property_id', $properties)
                        ->where('is_approved', true);
                    $totalReviews = $reviews->count();
                    $averageRating = $totalReviews > 0 ? $reviews->avg('rating') : 0;
                }
            } else {
                // For clients, get average rating from reviews they've written
                $reviews = $user->reviews()->where('is_approved', true);
                $totalReviews = $reviews->count();
                $averageRating = $totalReviews > 0 ? $reviews->avg('rating') : 0;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'user' => new UserResource($user),
                    'account_stats' => [
                        'member_since' => $user->created_at->format('Y-m-d'),
                        'last_login' => $user->updated_at->format('Y-m-d H:i'),
                        'total_bookings' => $user->reservations()->count(),
                        'total_reviews' => $totalReviews,
                        'average_rating' => round($averageRating, 1),
                        'is_verified' => $user->email_verified_at !== null,
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب معلومات الحساب',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update account information.
     */
    public function updateAccountInfo(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $validator = Validator::make($request->all(), [
                'full_name' => 'sometimes|string|max:255',
                'email' => [
                    'sometimes',
                    'email',
                    Rule::unique('users', 'email')->ignore($user->id)
                ],
                'phone' => [
                    'sometimes',
                    'string',
                    'regex:/^(009665|9665|\+9665|05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/',
                    Rule::unique('users', 'phone')->ignore($user->id)
                ],
                'birthdate' => 'sometimes|date|before:today',
                'gender' => 'sometimes|in:1,2',
                'bio' => 'sometimes|string|max:500',
                'profile_picture' => 'sometimes|image|mimes:jpeg,png,jpg|max:2048',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $data = $validator->validated();

            // Handle profile picture upload
            if ($request->hasFile('profile_picture')) {
                // Delete old profile picture if exists
                if ($user->profile_picture) {
                    Storage::disk('public')->delete('users/' . $user->profile_picture);
                }
                
                $file = $request->file('profile_picture');
                $filename = $file->hashName();
                $file->storeAs('users', $filename, 'public');
                $data['profile_picture'] = $filename;
            }

            $user->update($data);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث معلومات الحساب بنجاح',
                'data' => new UserResource($user->fresh()),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث معلومات الحساب',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Change password.
     */
    public function changePassword(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'current_password' => 'required|string',
                'new_password' => 'required|string|min:8|confirmed',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $user = Auth::user();

            // Check current password
            if (!Hash::check($request->current_password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'كلمة المرور الحالية غير صحيحة',
                ], 422);
            }

            // Update password
            $user->update([
                'password' => Hash::make($request->new_password),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تغيير كلمة المرور بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير كلمة المرور',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get account security settings.
     */
    public function getSecuritySettings(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'two_factor_enabled' => false, // Placeholder for future implementation
                    'login_sessions' => [
                        [
                            'device' => 'Mobile App',
                            'location' => 'الرياض، السعودية',
                            'last_active' => now()->format('Y-m-d H:i'),
                            'is_current' => true,
                        ]
                    ],
                    'recent_activities' => [
                        [
                            'action' => 'تسجيل الدخول',
                            'timestamp' => now()->subHours(2)->format('Y-m-d H:i'),
                            'device' => 'Mobile App',
                        ],
                        [
                            'action' => 'تحديث الملف الشخصي',
                            'timestamp' => now()->subDays(1)->format('Y-m-d H:i'),
                            'device' => 'Mobile App',
                        ],
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إعدادات الأمان',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Deactivate account.
     */
    public function deactivateAccount(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'reason' => 'required|string|max:500',
                'password' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $user = Auth::user();

            // Verify password
            if (!Hash::check($request->password, $user->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'كلمة المرور غير صحيحة',
                ], 422);
            }

            // Deactivate account (soft delete)
            $user->update([
                'is_active' => false,
                'deactivation_reason' => $request->reason,
                'deactivated_at' => now(),
            ]);

            // Revoke all tokens
            $user->tokens()->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء تفعيل الحساب بنجاح',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء تفعيل الحساب',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export account data.
     */
    public function exportAccountData(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $data = [
                'personal_info' => [
                    'full_name' => $user->full_name,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'birthdate' => $user->birthdate,
                    'gender' => $user->gender,
                    'bio' => $user->bio,
                    'member_since' => $user->created_at->format('Y-m-d'),
                ],
                'bookings' => $user->reservations()->with('item')->get()->map(function ($reservation) {
                    return [
                        'property_name' => $reservation->item->title ?? 'غير متاح',
                        'check_in' => $reservation->check_in,
                        'check_out' => $reservation->check_out,
                        'total_price' => $reservation->total_price,
                        'status' => $reservation->status,
                        'created_at' => $reservation->created_at->format('Y-m-d'),
                    ];
                }),
                'reviews' => $user->reviews()->with('item')->get()->map(function ($review) {
                    return [
                        'property_name' => $review->item->title ?? 'غير متاح',
                        'rating' => $review->rating,
                        'comment' => $review->comment,
                        'created_at' => $review->created_at->format('Y-m-d'),
                    ];
                }),
            ];

            return response()->json([
                'success' => true,
                'message' => 'تم تصدير بيانات الحساب بنجاح',
                'data' => $data,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير بيانات الحساب',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
