<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class UserResource extends JsonResource
{
    private $token;
    private $accessToken;
    private $unset;

    /**
     * User resource construct.
     *
     * @param  mixed  $collection
     * @param  \Laravel\Passport\Token|null  $token
     * @param  String|null  $accessToken
     */
    public function __construct($collection, $token = null, $accessToken = null, $unset = false)
    {
        parent::__construct($collection);
        $this->token = $token;
        $this->accessToken = $accessToken;
        $this->unset = $unset;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        if ($this->unset) {
            $this->email = '';
        }

        $lang = $request->hasHeader('X-localization') && in_array($request->Header('X-localization'), ['ar', 'en']) ? $request->Header('X-localization') : 'ar';
        $resource = [
            'user' => [
                'id' => (int) $this->id,
                'full_name' => $this->name ? $this->name : 'User@' . ceil($this->id * 23),
                'username' => $this->username,
                'bio' => $this->bio,
                'token' => $this->login_token,
                'email' => $this->email,
                'phone' => $this->phone,
                'gender' => (int) $this->gender,
                'birthdate' => $this->birthdate,
                'referral' => $this->referral ?? "",
                'otp_approved' => $this->otp_approved ? true : false,
                'is_guest' => $this->is_guest ? true : false,
                'is_hoster_mode' => $this->is_hoster_mode ? true : false,
                'user_type_id' => $this->user_type_id ?? 2,
                'profile_picture' => $this->profile_picture ? url("storage/clients/$this->profile_picture") : 'https://w7.pngwing.com/pngs/184/113/png-transparent-user-profile-computer-icons-profile-heroes-black-silhouette-thumbnail.png',
            ]
        ];
        $resource['access_token'] = !empty($this->accessToken) ? $this->accessToken : null;
        $resource['token_type'] = !empty($this->accessToken) ? 'Bearer' : null;
        if ($this->token) {
            $resource['expires_at'] = Carbon::parse($this->token->expires_at)->toDateTimeString();
        }

        return $resource;
    }

}
