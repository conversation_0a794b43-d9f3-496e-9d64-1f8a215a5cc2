<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EarlyAccessFeature extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_en',
        'name_ar',
        'description_en',
        'description_ar',
        'icon',
        'status',
        'progress_percentage',
        'estimated_release_date',
        'requirements',
        'is_premium',
        'order',
        'is_active',
    ];

    protected $casts = [
        'requirements' => 'array',
        'is_premium' => 'boolean',
        'is_active' => 'boolean',
        'estimated_release_date' => 'date',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'status_display',
        'status_color',
        'enrollment_count',
        'waitlist_count',
        'average_rating',
    ];

    /**
     * Get users enrolled in this feature
     */
    public function enrolledUsers()
    {
        return $this->belongsToMany(User::class, 'user_early_access', 'feature_id', 'user_id')
                    ->withPivot(['status', 'feedback', 'rating', 'enrolled_at', 'last_used_at'])
                    ->withTimestamps();
    }

    /**
     * Get users with enrolled status
     */
    public function activeUsers()
    {
        return $this->enrolledUsers()->wherePivot('status', 'enrolled');
    }

    /**
     * Get users on waitlist
     */
    public function waitlistUsers()
    {
        return $this->enrolledUsers()->wherePivot('status', 'waitlist');
    }

    /**
     * Scope to get active features
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get features by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get beta features
     */
    public function scopeBeta($query)
    {
        return $query->where('status', 'beta');
    }

    /**
     * Scope to get coming soon features
     */
    public function scopeComingSoon($query)
    {
        return $query->where('status', 'coming_soon');
    }

    /**
     * Scope to order by display order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order')->orderBy('created_at');
    }

    /**
     * Get localized name
     */
    public function getNameAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Get localized description
     */
    public function getDescriptionAttribute()
    {
        return app()->getLocale() === 'ar' ? $this->description_ar : $this->description_en;
    }

    /**
     * Check if feature is available for beta testing
     */
    public function isBeta()
    {
        return $this->status === 'beta';
    }

    /**
     * Check if feature is coming soon
     */
    public function isComingSoon()
    {
        return $this->status === 'coming_soon';
    }

    /**
     * Check if feature is in development
     */
    public function isInDevelopment()
    {
        return $this->status === 'development';
    }

    /**
     * Get status display name
     */
    public function getStatusDisplayAttribute()
    {
        $statusMap = [
            'development' => 'قيد التطوير',
            'beta' => 'نسخة تجريبية',
            'coming_soon' => 'قريباً',
            'released' => 'متاح',
        ];
        
        return $statusMap[$this->status] ?? $this->status;
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute()
    {
        $colorMap = [
            'development' => '#FF9500',
            'beta' => '#007AFF',
            'coming_soon' => '#8E8E93',
            'released' => '#34C759',
        ];
        
        return $colorMap[$this->status] ?? '#8E8E93';
    }

    /**
     * Get enrollment count
     */
    public function getEnrollmentCountAttribute()
    {
        try {
            return $this->belongsToMany(User::class, 'user_early_access', 'feature_id', 'user_id')
                ->wherePivot('status', 'enrolled')
                ->count();
        } catch (\Exception $e) {
            \Log::error('Error getting enrollment count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get waitlist count
     */
    public function getWaitlistCountAttribute()
    {
        try {
            return $this->belongsToMany(User::class, 'user_early_access', 'feature_id', 'user_id')
                ->wherePivot('status', 'waitlist')
                ->count();
        } catch (\Exception $e) {
            \Log::error('Error getting waitlist count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get average rating
     */
    public function getAverageRatingAttribute()
    {
        try {
            $avg = $this->belongsToMany(User::class, 'user_early_access', 'feature_id', 'user_id')
                ->whereNotNull('user_early_access.rating')
                ->avg('user_early_access.rating');
            return $avg ?? 0;
        } catch (\Exception $e) {
            \Log::error('Error getting average rating: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Check if feature is released
     */
    public function isReleased()
    {
        return $this->status === 'released';
    }

    /**
     * Check if user can enroll in this feature
     */
    public function canUserEnroll(User $user)
    {
        try {
            // Check if feature is available for enrollment
            if (!$this->is_active || $this->status === 'released') {
                return false;
            }

            // Check if user is already enrolled
            if ($this->enrolledUsers()->where('user_id', $user->id)->exists()) {
                return false;
            }

            // Check premium requirements
            if ($this->is_premium && !$user->isPremium()) {
                return false;
            }

            // Check other requirements
            $requirements = $this->requirements;
            if ($requirements && is_array($requirements)) {
                foreach ($requirements as $requirement) {
                    if (!$this->checkRequirement($user, $requirement)) {
                        return false;
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Error in canUserEnroll: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check specific requirement
     */
    private function checkRequirement(User $user, $requirement)
    {
        switch ($requirement['type'] ?? '') {
            case 'user_type':
                return $user->user_type_id == $requirement['value'];
            case 'min_properties':
                return $user->serviceCategoryItems()->count() >= $requirement['value'];
            case 'min_bookings':
                // Implement booking count check when booking system is available
                return true;
            default:
                return true;
        }
    }
}
