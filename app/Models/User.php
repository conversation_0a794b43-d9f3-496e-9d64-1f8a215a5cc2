<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use Lara<PERSON>\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;
    use HasRoles;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'profile_photo_url',
        'full_name',
    ];

    public function type()
    {
        return $this->belongsTo(UserTypes::class, 'user_type_id', 'id');
    }

    public function main_account()
    {
        return $this->belongsTo(User::class, 'main_account_id', 'id');
    }

    /**
     * Get user's service category items (properties)
     */
    public function serviceCategoryItems()
    {
        return $this->hasMany(\App\Models\Admin\ServiceCategoryItem::class);
    }

    /**
     * Friendships where this user is the requester
     */
    public function sentFriendRequests()
    {
        return $this->hasMany(Friendship::class, 'user_id');
    }

    /**
     * Friendships where this user is the recipient
     */
    public function receivedFriendRequests()
    {
        return $this->hasMany(Friendship::class, 'friend_id');
    }

    /**
     * Get all friends (accepted friendships)
     */
    public function friends()
    {
        $sentFriends = $this->sentFriendRequests()->accepted()->with('friend')->get()->pluck('friend');
        $receivedFriends = $this->receivedFriendRequests()->accepted()->with('user')->get()->pluck('user');

        return $sentFriends->merge($receivedFriends);
    }

    /**
     * Get pending friend requests sent by this user
     */
    public function pendingSentRequests()
    {
        return $this->sentFriendRequests()->pending()->with('friend');
    }

    /**
     * Get pending friend requests received by this user
     */
    public function pendingReceivedRequests()
    {
        return $this->receivedFriendRequests()->pending()->with('user');
    }

    /**
     * Send friend request to another user
     */
    public function sendFriendRequest($friendId)
    {
        // Check if friendship already exists
        $existingFriendship = Friendship::where(function ($query) use ($friendId) {
            $query->where('user_id', $this->id)->where('friend_id', $friendId);
        })->orWhere(function ($query) use ($friendId) {
            $query->where('user_id', $friendId)->where('friend_id', $this->id);
        })->first();

        if ($existingFriendship) {
            return false; // Friendship already exists
        }

        return Friendship::create([
            'user_id' => $this->id,
            'friend_id' => $friendId,
            'status' => 'pending',
        ]);
    }

    /**
     * Check if this user is friends with another user
     */
    public function isFriendsWith($userId)
    {
        return Friendship::where(function ($query) use ($userId) {
            $query->where('user_id', $this->id)->where('friend_id', $userId);
        })->orWhere(function ($query) use ($userId) {
            $query->where('user_id', $userId)->where('friend_id', $this->id);
        })->where('status', 'accepted')->exists();
    }

    /**
     * Check if this user has sent a friend request to another user
     */
    public function hasSentFriendRequestTo($userId)
    {
        return $this->sentFriendRequests()->where('friend_id', $userId)->where('status', 'pending')->exists();
    }

    /**
     * Check if this user has received a friend request from another user
     */
    public function hasReceivedFriendRequestFrom($userId)
    {
        return $this->receivedFriendRequests()->where('user_id', $userId)->where('status', 'pending')->exists();
    }

    /**
     * Co-host relationships where this user is the main host
     */
    public function coHostRelationships()
    {
        return $this->hasMany(CoHost::class, 'host_id');
    }

    /**
     * Co-host invitations where this user is invited as co-host
     */
    public function coHostInvitations()
    {
        return $this->hasMany(CoHost::class, 'co_host_id');
    }

    /**
     * Get user's reservations
     */
    public function reservations()
    {
        return $this->hasMany(\App\Models\Admin\ServiceCategoryItemReservation::class);
    }

    /**
     * Get accepted co-hosts for this user's properties
     */
    public function acceptedCoHosts()
    {
        return $this->coHostRelationships()->accepted()->with('coHost');
    }

    /**
     * Get pending co-host invitations sent by this user
     */
    public function pendingCoHostInvitations()
    {
        return $this->coHostRelationships()->pending()->with('coHost');
    }

    /**
     * Get pending co-host invitations received by this user
     */
    public function pendingCoHostInvitationsReceived()
    {
        return $this->coHostInvitations()->pending()->with('host');
    }

    /**
     * Invite a user to be a co-host
     */
    public function inviteCoHost($coHostId, $propertyId = null, $permissions = null, $commission = 0, $message = null)
    {
        // Check if invitation already exists
        $existingInvitation = CoHost::where('host_id', $this->id)
            ->where('co_host_id', $coHostId)
            ->where('property_id', $propertyId)
            ->whereIn('status', ['pending', 'accepted'])
            ->first();

        if ($existingInvitation) {
            return false; // Invitation already exists
        }

        return CoHost::create([
            'host_id' => $this->id,
            'co_host_id' => $coHostId,
            'property_id' => $propertyId,
            'permissions' => $permissions ?? CoHost::getDefaultPermissions(),
            'commission_percentage' => $commission,
            'message' => $message,
            'status' => 'pending',
        ]);
    }

    /**
     * Check if this user is a co-host for another user
     */
    public function isCoHostFor($hostId, $propertyId = null)
    {
        $query = $this->coHostInvitations()
            ->where('host_id', $hostId)
            ->where('status', 'accepted');

        if ($propertyId) {
            $query->where('property_id', $propertyId);
        }

        return $query->exists();
    }

    /**
     * Early access features relationship
     */
    public function earlyAccessFeatures()
    {
        return $this->belongsToMany(EarlyAccessFeature::class, 'user_early_access', 'user_id', 'feature_id')
                    ->withPivot(['status', 'feedback', 'rating', 'enrolled_at', 'last_used_at'])
                    ->withTimestamps();
    }

    /**
     * Get enrolled early access features
     */
    public function enrolledEarlyAccessFeatures()
    {
        return $this->earlyAccessFeatures()->wherePivot('status', 'enrolled');
    }

    /**
     * Get waitlisted early access features
     */
    public function waitlistedEarlyAccessFeatures()
    {
        return $this->earlyAccessFeatures()->wherePivot('status', 'waitlist');
    }

    /**
     * Enroll in early access feature
     */
    public function enrollInEarlyAccess($featureId)
    {
        $feature = EarlyAccessFeature::find($featureId);

        if (!$feature || !$feature->canUserEnroll($this)) {
            return false;
        }

        // Check if already enrolled
        $existing = UserEarlyAccess::where('user_id', $this->id)
            ->where('feature_id', $featureId)
            ->first();

        if ($existing) {
            return false;
        }

        UserEarlyAccess::create([
            'user_id' => $this->id,
            'feature_id' => $featureId,
            'status' => 'enrolled',
            'enrolled_at' => now(),
        ]);

        return true;
    }

    /**
     * Check if user has access to early access feature
     */
    public function hasEarlyAccessTo($featureId)
    {
        return $this->enrolledEarlyAccessFeatures()
            ->where('feature_id', $featureId)
            ->exists();
    }

    /**
     * Check if user is premium (placeholder - implement based on your premium system)
     */
    public function isPremium()
    {
        // Implement premium check logic here
        return false;
    }

    /**
     * Get the user's notifications.
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Get the user's notification preferences.
     */
    public function notificationPreferences()
    {
        return $this->hasMany(NotificationPreference::class);
    }

    /**
     * Get the reviews written by this user
     */
    public function reviews()
    {
        return $this->hasMany(\App\Models\Review::class, 'user_id');
    }

    /**
     * Get the full name attribute (accessor for name field)
     */
    public function getFullNameAttribute()
    {
        return $this->name;
    }

    /**
     * Set the full name attribute (mutator for name field)
     */
    public function setFullNameAttribute($value)
    {
        $this->attributes['name'] = $value;
    }

    /**
     * Get the bio attribute with default value
     */
    public function getBioAttribute($value)
    {
        return $value ?? '';
    }

}
